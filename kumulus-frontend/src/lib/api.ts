import axios from "axios";
import { env } from "./env";
import { getSession } from "./auth";
import type { DashboardOverviewResponse, DashboardAnalyticsResponse } from "@/types/api";

// Create axios instance
const api = axios.create({
  baseURL: `${env.NEXT_PUBLIC_API_URL}/api`,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to include auth tokens
api.interceptors.request.use(async (config) => {
  try {
    const session = await getSession();
    if (session?.user) {
      // BetterAuth handles cookies automatically, but we can add additional headers if needed
      config.headers.Authorization = `Bearer ${session.session.token}`;
    }
  } catch (error) {
    console.error("Failed to get session:", error);
  }
  return config;
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Redirect to login on unauthorized access
      if (typeof window !== "undefined") {
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

// API methods
export const apiClient = {
  dashboard: {
    getOverview: async (): Promise<DashboardOverviewResponse> => {
      const response = await api.get("/dashboard/overview");
      return response.data;
    },
    getAnalytics: async (): Promise<DashboardAnalyticsResponse> => {
      const response = await api.get("/dashboard/analytics");
      return response.data;
    },
  },
};

export default api;
