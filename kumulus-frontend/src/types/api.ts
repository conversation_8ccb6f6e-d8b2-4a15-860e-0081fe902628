export interface User {
  id: string;
  name: string;
  email: string;
}

export interface DashboardStats {
  totalVMs: number;
  runningVMs: number;
  stoppedVMs: number;
  totalResources: number;
}

export interface ActivityItem {
  id: number;
  action: string;
  resource: string;
  timestamp: string;
  status: string;
}

export interface DashboardOverviewResponse {
  message: string;
  user: User;
  stats: DashboardStats;
  recentActivity: ActivityItem[];
}

export interface RequestsOverTime {
  date: string;
  requests: number;
}

export interface ProviderUsage {
  provider: string;
  usage: number;
}

export interface ErrorRates {
  total: number;
  errors: number;
  rate: string;
}

export interface AnalyticsData {
  requestsOverTime: RequestsOverTime[];
  providerUsage: ProviderUsage[];
  errorRates: ErrorRates;
}

export interface DashboardAnalyticsResponse {
  message: string;
  userId: string;
  analytics: AnalyticsData;
}

export interface ApiError {
  error: string;
}
