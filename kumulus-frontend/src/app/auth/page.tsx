"use client";

import { useState } from "react";
import { SignInForm } from "@/components/auth/sign-in-form";
import { SignUpForm } from "@/components/auth/sign-up-form";

export default function AuthPage() {
  const [isSignUp, setIsSignUp] = useState(false);

  const toggleMode = () => {
    setIsSignUp(!isSignUp);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to Kumulus
          </h1>
          <p className="text-gray-600">
            Your cloud infrastructure management platform
          </p>
        </div>
        
        {isSignUp ? (
          <SignUpForm onToggleMode={toggleMode} />
        ) : (
          <SignInForm onToggleMode={toggleMode} />
        )}
      </div>
    </div>
  );
}
