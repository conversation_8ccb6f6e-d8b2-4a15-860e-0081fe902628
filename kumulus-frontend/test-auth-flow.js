#!/usr/bin/env node

/**
 * Simple test script to verify the authentication flow
 * Run with: node test-auth-flow.js
 */

const API_BASE = 'http://localhost:8000/api';

async function testAuthFlow() {
  console.log('🧪 Testing Kumulus Frontend Authentication Flow\n');

  // Test data
  const testUser = {
    name: 'Test User',
    email: `test-${Date.now()}@example.com`,
    password: 'testpassword123'
  };

  try {
    // Test 1: Sign Up
    console.log('1️⃣ Testing Sign Up...');
    const signUpResponse = await fetch(`${API_BASE}/auth/email/sign-up`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        name: testUser.name,
        email: testUser.email,
        password: testUser.password,
      }),
    });

    if (signUpResponse.ok) {
      console.log('✅ Sign Up successful');
      const signUpData = await signUpResponse.json();
      console.log('   User created:', signUpData.user?.email || 'Unknown');
    } else {
      const error = await signUpResponse.text();
      console.log('❌ Sign Up failed:', signUpResponse.status, error);
      return;
    }

    // Test 2: Get Session (should be authenticated after sign up)
    console.log('\n2️⃣ Testing Session after Sign Up...');
    const sessionResponse = await fetch(`${API_BASE}/auth/get-session`, {
      method: 'GET',
      credentials: 'include',
    });

    if (sessionResponse.ok) {
      const sessionData = await sessionResponse.json();
      if (sessionData.user) {
        console.log('✅ Session valid after sign up');
        console.log('   User:', sessionData.user.email);
      } else {
        console.log('❌ No user in session');
      }
    } else {
      console.log('❌ Session check failed:', sessionResponse.status);
    }

    // Test 3: Sign Out
    console.log('\n3️⃣ Testing Sign Out...');
    const signOutResponse = await fetch(`${API_BASE}/auth/sign-out`, {
      method: 'POST',
      credentials: 'include',
    });

    if (signOutResponse.ok) {
      console.log('✅ Sign Out successful');
    } else {
      console.log('❌ Sign Out failed:', signOutResponse.status);
    }

    // Test 4: Sign In
    console.log('\n4️⃣ Testing Sign In...');
    const signInResponse = await fetch(`${API_BASE}/auth/email/sign-in`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password,
      }),
    });

    if (signInResponse.ok) {
      console.log('✅ Sign In successful');
      const signInData = await signInResponse.json();
      console.log('   User:', signInData.user?.email || 'Unknown');
    } else {
      const error = await signInResponse.text();
      console.log('❌ Sign In failed:', signInResponse.status, error);
      return;
    }

    // Test 5: Dashboard API (protected route)
    console.log('\n5️⃣ Testing Dashboard API (protected route)...');
    const dashboardResponse = await fetch(`${API_BASE}/dashboard/overview`, {
      method: 'GET',
      credentials: 'include',
    });

    if (dashboardResponse.ok) {
      const dashboardData = await dashboardResponse.json();
      console.log('✅ Dashboard API accessible');
      console.log('   Stats:', JSON.stringify(dashboardData.stats, null, 2));
    } else {
      console.log('❌ Dashboard API failed:', dashboardResponse.status);
    }

    console.log('\n🎉 Authentication flow test completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Check if backend is running
async function checkBackend() {
  try {
    const response = await fetch(`${API_BASE}/auth/get-session`);
    return true;
  } catch (error) {
    console.error('❌ Backend not accessible. Make sure it\'s running on http://localhost:8000');
    console.error('   Start with: cd kumulus-backend && deno run --env -A main.ts');
    return false;
  }
}

// Main execution
async function main() {
  const backendRunning = await checkBackend();
  if (backendRunning) {
    await testAuthFlow();
  }
}

main();
