# Kumulus Frontend

A Next.js frontend application for the Kumulus cloud infrastructure management platform, featuring BetterAuth authentication and shadcn/ui components.

## Features

- **Authentication**: Complete sign-in/sign-up flow using BetterAuth
- **Protected Routes**: Dashboard and other protected pages require authentication
- **Modern UI**: Built with shadcn/ui components and Tailwind CSS
- **Backend Integration**: Connects to Kumulus backend APIs
- **Dashboard**: Displays user statistics, recent activity, and analytics

## Prerequisites

- Node.js 18+
- Running Kumulus backend (see `../kumulus-backend/README.md`)
- PostgreSQL database (configured in backend)

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure environment variables**:
   Create or update `.env.local`:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8000/api
   NEXT_PUBLIC_AUTH_ENDPOINT=http://localhost:8000/api/auth
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Authentication Flow

1. **Landing Page**: Automatically redirects to `/auth` if not authenticated
2. **Sign Up**: Create a new account with name, email, and password
3. **Sign In**: Login with existing credentials
4. **Dashboard**: Protected page showing user data and statistics
5. **Sign Out**: Logout and redirect to authentication page

## Testing the Application

### Manual Testing Steps

1. **Start both backend and frontend**:
   ```bash
   # Terminal 1 - Backend
   cd kumulus-backend
   deno run --env -A main.ts

   # Terminal 2 - Frontend
   cd kumulus-frontend
   npm run dev
   ```

2. **Test Sign Up Flow**:
   - Go to http://localhost:3000
   - Should redirect to `/auth`
   - Fill in: Name, Email, Password, Confirm Password
   - Submit form → Should redirect to `/dashboard` on success

3. **Test Sign In Flow**:
   - Go to `/auth` and switch to sign in
   - Enter credentials → Should redirect to `/dashboard`

4. **Test Dashboard**:
   - Should display user info, statistics, and recent activity
   - "Sign Out" should work and redirect to `/auth`

## Project Structure

```
src/
├── app/
│   ├── auth/page.tsx          # Authentication page
│   ├── dashboard/page.tsx     # Protected dashboard
│   └── page.tsx               # Landing page (redirects)
├── components/
│   ├── auth/                  # Authentication components
│   └── ui/                    # shadcn/ui components
├── lib/
│   ├── api.ts                 # API client
│   ├── auth.ts                # BetterAuth setup
│   └── env.ts                 # Environment validation
└── types/
    └── api.ts                 # TypeScript types
```

## Technologies Used

- **Next.js 15** - React framework
- **BetterAuth** - Authentication
- **shadcn/ui** - UI components
- **Tailwind CSS** - Styling
- **TypeScript** - Type safety
