{"version": "0.1.0", "imports": {"@/": "./src/", "@types/pg": "npm:@types/pg@^8.15.4", "better-auth": "npm:better-auth@^1.3.7", "drizzle-kit": "npm:drizzle-kit@^0.31.4", "drizzle-orm": "npm:drizzle-orm@^0.44.3", "hono": "npm:hono@^4.8.5", "jose": "npm:jose@^6.0.12", "pg": "npm:pg@^8.16.3", "zod": "npm:zod@^4.0.10"}, "tasks": {"start": "deno run --env -A main.ts", "lint": "deno lint"}, "nodeModulesDir": "auto"}