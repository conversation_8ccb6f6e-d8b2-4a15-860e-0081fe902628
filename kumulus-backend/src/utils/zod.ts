// resource zod schema
import { z } from "zod";

// Ressources
export const zResourceSchema = z.object({
  name: z.string(),
  providerId: z.string().optional(),
  ipAddress: z.string().optional(),
  cpuCores: z.number().int().positive(),
  ram: z.number().int().positive(),
  storage: z.number().int().positive(),
  bandwidth: z.number().int().optional(),
  gpu: z.any().optional(),
  sshPublicKey: z.string().optional(),
  checkedSpecs: z.any().optional(),
});

export type Resource = z.infer<typeof zResourceSchema>;

// VMs
export const zVMSchema = z.object({
  username: z.string(),
  sshKey: z.string(),
  cpu: z.number().int().positive(),
  ram: z.number().int().positive(),
  storage: z.number().int().positive(),
  gpu: z.any().optional(),
});
