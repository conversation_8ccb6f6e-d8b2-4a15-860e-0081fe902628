import { Hono } from "hono";
import { updateResource } from "../db/queries/resources-queries.ts";
import {
  activateReservedTunnel,
  getVmSshPort,
  reserveVmSshTunnel,
  activateVmSshTunnel,
  getAnActiveBastion
} from "../db/queries/bastions-queries.ts";
import { db } from "../db/db.ts";
import { tunnels as tunnelSchema, bastions as bastionSchema } from "../drizzle/schema.ts";
import { eq } from "drizzle-orm";

// Smart Tunnel State Management
interface TunnelHealthCache {
  [tunnelId: string]: {
    lastChecked: number;
    status: 'active' | 'inactive' | 'unknown';
    consecutiveFailures: number;
    lastKnownGood: number;
  };
}

// In-memory cache for tunnel health (lightweight, no DB overhead)
const tunnelHealthCache: TunnelHealthCache = {};
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const MAX_CONSECUTIVE_FAILURES = 3;

// Smart tunnel validation functions
async function validateTunnelHealth(bastionIp: string, port: number, tunnelId: string): Promise<boolean> {
  const cacheKey = `${bastionIp}:${port}`;
  const now = Date.now();
  const cached = tunnelHealthCache[cacheKey];

  // Use cached result if recent and reliable
  if (cached && (now - cached.lastChecked) < CACHE_TTL) {
    if (cached.status === 'active' && cached.consecutiveFailures === 0) {
      return true;
    }
    if (cached.consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
      return false;
    }
  }

  // Perform lightweight connection test
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

    const response = await fetch(`http://${bastionIp}:${port}/health`, {
      method: 'GET',
      signal: controller.signal,
    }).catch(() => null);

    clearTimeout(timeoutId);

    const isHealthy = response?.ok || false;

    // Update cache
    tunnelHealthCache[cacheKey] = {
      lastChecked: now,
      status: isHealthy ? 'active' : 'inactive',
      consecutiveFailures: isHealthy ? 0 : (cached?.consecutiveFailures || 0) + 1,
      lastKnownGood: isHealthy ? now : (cached?.lastKnownGood || 0),
    };

    return isHealthy;
  } catch (error) {
    // Update cache with failure
    tunnelHealthCache[cacheKey] = {
      lastChecked: now,
      status: 'inactive',
      consecutiveFailures: (cached?.consecutiveFailures || 0) + 1,
      lastKnownGood: cached?.lastKnownGood || 0,
    };

    return false;
  }
}

async function smartTunnelStateUpdate(tunnelId: string, detectedState: 'active' | 'inactive', reason: string) {
  try {
    // Only update database if state actually changed
    const currentTime = new Date().toISOString();

    if (detectedState === 'inactive') {
      // Mark tunnel as inactive with reason
      await db.update(tunnelSchema)
        .set({
          status: "inactive",
          updatedAt: currentTime,
          // Store reason in a comment field if available, or log it
        })
        .where(eq(tunnelSchema.id, tunnelId));

      console.log(`🔴 Tunnel ${tunnelId} marked inactive: ${reason}`);
    } else {
      // Reactivate tunnel
      await db.update(tunnelSchema)
        .set({
          status: "active",
          updatedAt: currentTime,
        })
        .where(eq(tunnelSchema.id, tunnelId));

      console.log(`🟢 Tunnel ${tunnelId} reactivated: ${reason}`);
    }
  } catch (error) {
    console.error(`❌ Failed to update tunnel state for ${tunnelId}:`, error);
  }
}

const temp = new Hono();

temp.get("/test", async (c) => {

  // TODO: TO remove
  // retrieve Trusted origins and cors origins from env
  const trustedOrigins = Deno.env.get("TRUSTED_ORIGINS")?.split(",");
  const corsOrigins = Deno.env.get("CORS_ORIGINS")?.split(",");
  return c.json({
    message: "Hello from the backend!",
    trustedOrigins,
    corsOrigins,
  });
});

// Post resources specs sent form the virtual machine to be matched against what's declared
temp.post("/verified-specs", async (c) => {
  try {
    const checkedSpecs = await c.req.json();
    console.log("🟢 Received verification report:", checkedSpecs);

    // Insert Verified specs
    await updateResource(checkedSpecs.resourceId, { checkedSpecs });
    return c.json({
      message: "Specs verified successfully",
      resourceId: checkedSpecs.resourceId,
      specs: checkedSpecs,
    });
  } catch (error) {
    console.error("Error verifying specs:", error);
    return c.json({ error: "Failed to verify specs" }, 500);
  }
});

// Installation progress endpoint - called by setup script
temp.post("/:id/installation", async (c) => {
  try {
    const resourceId = c.req.param("id");

    const body = await c.req.json();
    const { step, status, message, timestamp } = body;

    console.log(
      `📊 Installation progress for resource ${resourceId}: ${step} - ${status} - ${message}`,
    );

    // Update resource status based on installation progress
    const updateData: any = {
      updatedAt: new Date(),
    };

    // Set status based on installation step
    if (step === "mark_ready" && status === "completed") {
      updateData.status = "ready";
    } else if (status === "failed") {
      updateData.status = "error";
      updateData.errorMessage = message;
    } else if (status === "in_progress") {
      updateData.status = "installing";
    }

    await updateResource(resourceId, updateData);

    return c.json({
      message: "Installation progress updated",
      resourceId,
      step,
      status,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error updating installation progress:", error);
    return c.json({ error: "Failed to update installation progress" }, 500);
  }
});

// Mark the resource ready (authenticated endpoint)
temp.post("/mark-ready/:id", async (c) => {
  try {
    const resourceId = c.req.param("id");

    // For now, we'll allow this without strict user authentication
    // since it's called by the installation script
    // In production, you'd want to validate the provider token

    console.log(`✅ Marking resource ${resourceId} as ready`);

    // Update resource status based on installation progress
    const updateData: any = {
      status: "ready",
      updatedAt: new Date(),
    };

    await updateResource(resourceId, updateData);

    // CRITICAL: Activate the reserved tunnel (prevents race conditions)
    await activateReservedTunnel(resourceId);
    console.log(`🔓 Activated reserved tunnel for resource ${resourceId}`);

    return c.json({
      message: "Resource marked as ready successfully",
      resourceId,
      status: "ready",
    });
  } catch (error) {
    console.error("Error marking resource as ready:", error);
    return c.json({ error: "Failed to mark resource as ready" }, 500);
  }
});

// Health check endpoint for agents
temp.get("/health", async (c) => {
  return c.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "0.1.0-alpha",
    uptime: process.uptime(),
  });
});

// Agent registration endpoint (called when agent starts)
temp.post("/agent-register/:id", async (c) => {
  try {
    const resourceId = c.req.param("id");
    const body = await c.req.json().catch(() => ({}));

    console.log(`🤖 Agent registering for resource ${resourceId}`);

    // Update resource with agent information
    const updateData: any = {
      updatedAt: new Date(),
      // Could store agent version, capabilities, etc.
    };

    if (body.agentVersion) {
      updateData.agentVersion = body.agentVersion;
    }

    await updateResource(resourceId, updateData);

    return c.json({
      message: "Agent registered successfully",
      resourceId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error registering agent:", error);
    return c.json({ error: "Failed to register agent" }, 500);
  }
});

// Tunnel status endpoint
temp.get("/tunnel-status/:id", async (c) => {
  try {
    const resourceId = c.req.param("id");

    // This would check tunnel status in a real implementation
    // For now, return basic status
    return c.json({
      resourceId,
      tunnelStatus: "active",
      lastCheck: new Date().toISOString(),
      message: "Tunnel status check - implementation pending",
    });
  } catch (error) {
    console.error("Error checking tunnel status:", error);
    return c.json({ error: "Failed to check tunnel status" }, 500);
  }
});

// Request VM SSH tunnel port (called by Flare Agent)
temp.post("/request-vm-ssh-tunnel", async (c) => {
  try {
    const body = await c.req.json();
    const { vmContainerId, sshUsername } = body;

    if (!vmContainerId || !sshUsername) {
      return c.json({
        error: "Missing required parameters",
        required: ["vmContainerId", "sshUsername"]
      }, 400);
    }

    console.log(`🔌 VM SSH tunnel request for container: ${vmContainerId}, user: ${sshUsername}`);

    // Get an active bastion
    const activeBastions = await getAnActiveBastion();
    if (!activeBastions || activeBastions.length === 0) {
      return c.json({ error: "No active bastion available" }, 503);
    }

    const bastion = activeBastions[0];

    // Get next available VM SSH port
    const assignedPort = await getVmSshPort(bastion.id);

    // Reserve the tunnel port
    await reserveVmSshTunnel(bastion.id, vmContainerId, assignedPort, sshUsername);

    console.log(`✅ VM SSH tunnel reserved: ${bastion.ip}:${assignedPort} -> ${vmContainerId}`);

    return c.json({
      success: true,
      bastionIp: bastion.ip,
      assignedPort,
      vmContainerId,
      sshUsername,
      tunnelCommand: `ssh -N -R ${assignedPort}:localhost:22 ${sshUsername}@${bastion.ip}`,
      sshAccess: `ssh ${sshUsername}@${bastion.ip} -p ${assignedPort}`,
    });

  } catch (error) {
    console.error("Error requesting VM SSH tunnel:", error);
    return c.json({
      error: "Failed to request VM SSH tunnel",
      message: error.message
    }, 500);
  }
});

// Activate VM SSH tunnel (called by Flare Agent after tunnel is established)
temp.post("/activate-vm-ssh-tunnel", async (c) => {
  try {
    const body = await c.req.json();
    const { vmContainerId } = body;

    if (!vmContainerId) {
      return c.json({ error: "vmContainerId is required" }, 400);
    }

    console.log(`🔓 Activating VM SSH tunnel for container: ${vmContainerId}`);

    // Activate the reserved tunnel
    await activateVmSshTunnel(vmContainerId);

    console.log(`✅ VM SSH tunnel activated for: ${vmContainerId}`);

    return c.json({
      success: true,
      vmContainerId,
      status: "active",
      message: "VM SSH tunnel activated successfully"
    });

  } catch (error) {
    console.error("Error activating VM SSH tunnel:", error);
    return c.json({
      error: "Failed to activate VM SSH tunnel",
      message: error.message
    }, 500);
  }
});

// Smart tunnel health validation endpoint (lightweight, event-driven)
temp.post("/validate-tunnel-health", async (c) => {
  try {
    const body = await c.req.json();
    const { bastionIp, port, tunnelId, vmContainerId } = body;

    if (!bastionIp || !port) {
      return c.json({ error: "bastionIp and port are required" }, 400);
    }

    console.log(`🔍 Validating tunnel health: ${bastionIp}:${port}`);

    // Perform smart validation
    const isHealthy = await validateTunnelHealth(bastionIp, port, tunnelId || `${bastionIp}:${port}`);

    // If tunnel is unhealthy and we have tunnel ID, update database
    if (!isHealthy && tunnelId) {
      await smartTunnelStateUpdate(tunnelId, 'inactive', 'Health check failed');
    }

    return c.json({
      success: true,
      bastionIp,
      port,
      isHealthy,
      status: isHealthy ? 'active' : 'inactive',
      timestamp: new Date().toISOString(),
      cached: tunnelHealthCache[`${bastionIp}:${port}`] || null,
    });

  } catch (error) {
    console.error("Error validating tunnel health:", error);
    return c.json({
      error: "Failed to validate tunnel health",
      message: error.message
    }, 500);
  }
});

// Provider reconnection handler (called when provider comes back online)
temp.post("/provider-reconnected", async (c) => {
  try {
    const body = await c.req.json();
    const { resourceId, bastionIp, agentPort } = body;

    if (!resourceId || !bastionIp || !agentPort) {
      return c.json({
        error: "resourceId, bastionIp, and agentPort are required"
      }, 400);
    }

    console.log(`🔄 Provider reconnected: ${resourceId} at ${bastionIp}:${agentPort}`);

    // Clear any cached negative health status
    const cacheKey = `${bastionIp}:${agentPort}`;
    delete tunnelHealthCache[cacheKey];

    // Validate tunnel health
    const isHealthy = await validateTunnelHealth(bastionIp, agentPort, resourceId);

    if (isHealthy) {
      // Reactivate any inactive tunnels for this provider
      await smartTunnelStateUpdate(resourceId, 'active', 'Provider reconnected');

      return c.json({
        success: true,
        resourceId,
        status: 'active',
        message: 'Provider reconnected and tunnel reactivated',
      });
    } else {
      return c.json({
        success: false,
        resourceId,
        status: 'inactive',
        message: 'Provider reconnected but tunnel health check failed',
      });
    }

  } catch (error) {
    console.error("Error handling provider reconnection:", error);
    return c.json({
      error: "Failed to handle provider reconnection",
      message: error.message
    }, 500);
  }
});

// Tunnel state cleanup (removes stale cache entries)
temp.post("/cleanup-tunnel-cache", async (c) => {
  try {
    const now = Date.now();
    let cleanedCount = 0;

    // Remove stale cache entries
    for (const [key, value] of Object.entries(tunnelHealthCache)) {
      if ((now - value.lastChecked) > (CACHE_TTL * 2)) { // Double TTL for cleanup
        delete tunnelHealthCache[key];
        cleanedCount++;
      }
    }

    console.log(`🧹 Cleaned ${cleanedCount} stale tunnel cache entries`);

    return c.json({
      success: true,
      cleanedCount,
      remainingEntries: Object.keys(tunnelHealthCache).length,
      message: 'Tunnel cache cleanup completed',
    });

  } catch (error) {
    console.error("Error cleaning tunnel cache:", error);
    return c.json({
      error: "Failed to clean tunnel cache",
      message: error.message
    }, 500);
  }
});

// Debug endpoint to check raw bastion data
temp.get("/debug-bastions", async (c) => {
  try {
    const bastions = await db.select().from(bastionSchema);

    return c.json({
      success: true,
      bastions: bastions.map(bastion => ({
        id: bastion.id,
        name: bastion.name,
        ipAddress: bastion.ipAddress,
        sshPublicKey: bastion.sshPublicKey,
        sshKeyLength: bastion.sshPublicKey?.length || 0,
        sshKeyPreview: bastion.sshPublicKey?.substring(0, 100) + '...',
        containsSqlFragments: bastion.sshPublicKey?.includes('where id =') || false,
        isActive: bastion.isActive,
      })),
    });
  } catch (error) {
    console.error("Error getting bastion debug info:", error);
    return c.json({
      error: "Failed to get bastion debug info",
      message: error.message
    }, 500);
  }
});

export { temp };
