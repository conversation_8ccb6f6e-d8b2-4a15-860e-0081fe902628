import { Hono } from "hono";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";
import {
  getDeveloperByUserId,
  updateDeveloperSSHKey,
} from "../db/queries/developers-queries.ts";

const profile = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Apply auth middleware to all routes in this router
profile.use("*", authMiddleware);

// Get user profile
profile.get("/", async (c) => {
  const user = c.get("user");

  // Get developer record to include SSH key
  const developer = await getDeveloperByUserId(user.id);

  return c.json({
    message: "User profile",
    profile: {
      id: user.id,
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      image: user.image,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      sshPublicKey: developer?.sshPublicKey || "",
      walletAddress: developer?.walletAddress || "", // From developer record
    },
  });
});

// Update user profile
profile.put("/", async (c) => {
  const user = c.get("user");
  const body = await c.req.json();

  try {
    // Get developer record
    const developer = await getDeveloperByUserId(user.id);

    if (!developer) {
      return c.json({ error: "Developer record not found" }, 404);
    }

    // Update SSH key if provided
    if (body.sshPublicKey !== undefined) {
      await updateDeveloperSSHKey(developer.id, body.sshPublicKey);
    }

    // Get updated developer record
    const updatedDeveloper = await getDeveloperByUserId(user.id);

    return c.json({
      message: "Profile updated successfully",
      profile: {
        id: user.id,
        name: body.name || user.name,
        email: user.email, // Email shouldn't be changed here
        emailVerified: user.emailVerified,
        image: body.image || user.image,
        sshPublicKey: updatedDeveloper?.sshPublicKey || "",
        walletAddress: updatedDeveloper?.walletAddress || "",
        createdAt: user.createdAt,
        updatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Error updating profile:", error);
    return c.json({
      error: "Failed to update profile",
      message: error.message,
    }, 500);
  }
});

// Settings endpoints removed - functionality moved to profile management

export { profile };
