import { Hono } from "hono";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";

// In-memory provider status storage (in production, this would be a database)
interface ProviderStatus {
  id: string;
  name: string;
  installationStatus: "pending" | "in_progress" | "completed" | "failed";
  agentStatus: "offline" | "starting" | "running" | "error";
  lastHeartbeat: string | null;
  installationProgress: number; // 0-100
  errorMessage?: string;
  systemInfo?: {
    cpu: string;
    memory: string;
    disk: string;
  };
}

// Mock provider status data
const providerStatusMap = new Map<string, ProviderStatus>([
  ["provider-1", {
    id: "provider-1",
    name: "AWS Provider East",
    installationStatus: "completed",
    agentStatus: "running",
    lastHeartbeat: new Date().toISOString(),
    installationProgress: 100,
    systemInfo: { cpu: "45%", memory: "60%", disk: "30%" },
  }],
  ["provider-2", {
    id: "provider-2",
    name: "Google Cloud West",
    installationStatus: "completed",
    agentStatus: "running",
    lastHeartbeat: new Date(Date.now() - 120000).toISOString(), // 2 minutes ago
    installationProgress: 100,
    systemInfo: { cpu: "30%", memory: "45%", disk: "55%" },
  }],
  ["provider-3", {
    id: "provider-3",
    name: "Azure Provider Central",
    installationStatus: "in_progress",
    agentStatus: "offline",
    lastHeartbeat: null,
    installationProgress: 75,
  }],
  ["provider-4", {
    id: "provider-4",
    name: "DigitalOcean Provider",
    installationStatus: "failed",
    agentStatus: "error",
    lastHeartbeat: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
    installationProgress: 45,
    errorMessage: "Docker installation failed: Permission denied",
  }],
  ["provider-5", {
    id: "provider-5",
    name: "Linode Provider",
    installationStatus: "pending",
    agentStatus: "offline",
    lastHeartbeat: null,
    installationProgress: 0,
  }],
]);

const dashboard = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Apply auth middleware to all routes in this router
dashboard.use("*", authMiddleware);

// Dashboard overview endpoint
dashboard.get("/overview", async (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json({ error: "User not found" }, 401);
  }

  // Developer-specific dashboard data
  // For now, return mock data since we don't have VM/app queries implemented yet
  const totalVMs = 0; // Total VMs created by developer
  const runningVMs = 0; // Currently running VMs
  const totalApps = 0; // Total apps deployed by developer

  return c.json({
    message: "Dashboard overview",
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
    },
    stats: {
      totalVMs,
      runningVMs,
      stoppedVMs: totalVMs - runningVMs,
      totalResources: totalVMs + totalApps,
    },
    recentActivity: [
      {
        id: 1,
        action: "Dashboard accessed",
        resource: "Developer Console",
        timestamp: new Date().toISOString(),
        status: "success",
      },
    ],
  });
});

// Dashboard analytics endpoint
dashboard.get("/analytics", (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json({ error: "User not found" }, 401);
  }

  return c.json({
    message: "Dashboard analytics",
    userId: user.id,
    analytics: {
      requestsOverTime: [
        { date: "2024-01-01", requests: 45 },
        { date: "2024-01-02", requests: 67 },
        { date: "2024-01-03", requests: 89 },
        { date: "2024-01-04", requests: 123 },
        { date: "2024-01-05", requests: 156 },
      ],
      providerUsage: [
        { provider: "AWS S3", usage: 45 },
        { provider: "Google Drive", usage: 32 },
        { provider: "Dropbox", usage: 23 },
      ],
      errorRates: {
        total: 1247,
        errors: 12,
        rate: "0.96%",
      },
    },
  });
});

export { dashboard };
