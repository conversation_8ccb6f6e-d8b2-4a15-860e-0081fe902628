import { Hono } from "hono";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";
import { insertDeveloper } from "../db/queries/developers-queries.ts";

const developers = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Apply auth middleware to all routes in this router
developers.use("*", authMiddleware);

// Link developer to user
developers.post("/linkdeveloper", async (c) => {
  try {
    const body = await c.req.json();
    const userId = body.userId;

    if (!userId) {
      return c.json({ error: "userId is required" }, 400);
    }

    // Generate a unique wallet address to avoid constraint violations
    const uniqueWalletAddress = `0x${Date.now().toString(16)}${
      Math.random().toString(16).substr(2, 8)
    }`;

    // Insert developer into database
    const result = await insertDeveloper(userId, "", uniqueWalletAddress);
    return c.json({
      message: "<PERSON><PERSON>per linked successfully",
      developer: result,
    });
  } catch (error) {
    console.error("Error linking developer:", error);

    // Check if it's a duplicate key error
    if (error.message && error.message.includes("duplicate key")) {
      return c.json({
        error: "Developer already linked",
        message: "This user already has a developer account",
      }, 409);
    }

    return c.json({
      error: "Failed to link developer",
      message: error.message || "Internal server error",
    }, 500);
  }
});

export { developers };
