import { Hono } from "hono";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";
import { insertProvider } from "../db/queries/providers-queries.ts";

const providers = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Apply auth middleware to all routes in this router
providers.use("*", authMiddleware);

// Link provider to user
providers.post("/linkprovider", async (c) => {
  // retrieve userId from the body;
  try {
    const body = await c.req.json();
    const userId = body.userId;
    // Insert provider into database
    const result = await insertProvider(userId, "", "0x0");
    return c.json({
      message: "Provider linked successfully",
      provider: result,
    });
  } catch (error) {
    return c.json({ error: "Invalid request" }, 400);
  }
});

export { providers };
