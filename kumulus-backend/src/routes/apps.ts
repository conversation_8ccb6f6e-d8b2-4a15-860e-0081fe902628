import { Hono } from "hono";
import { z } from "zod";
import { APP_TYPES } from "../utils/constants.ts";
import {
  insertAppDeployment,
  insertDeploymentContainers,
  updateDeploymentContainersStatus,
} from "../db/queries/apps-deployments-queries.ts";
import {
  getProviderIdByUserId,
  selectProvider,
} from "../db/queries/providers-queries.ts";
import { getDeveloperByUserId } from "../db/queries/developers-queries.ts";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";

// Validation Schemas
const createVMSchema = z.object({
  username: z.string().min(1),
  sshKey: z.string().min(1),
  cpu: z.number().min(1).max(8),
  memory: z.string().regex(/^\d+[mg]$/),
  disk: z.string().regex(/^\d+[mg]$/),
});

const createAppSchema = z.object({
  appType: z.enum(APP_TYPES as [string, ...string[]]),
  cpu: z.number().min(1).max(4),
  memory: z.string().regex(/^\d+[mg]$/),
  devSSHKey: z.string().optional(),
});

const deploymentControlSchema = z.object({
  deploymentId: z.string().min(1),
});

const apps = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Apply auth middleware to all routes
apps.use("*", authMiddleware);

// Get Apps for the authenticated user
apps.get("/", async (c) => {
  try {
    const user = c.get("user");
    if (!user) {
      return c.json({ error: "User not found" }, 401);
    }

    // Get developer record for this user
    const developer = await getDeveloperByUserId(user.id);
    if (!developer) {
      // If no developer record, return empty array
      return c.json({
        apps: [],
        total: 0,
      });
    }

    // For now, return empty array since we don't have app deployment queries implemented yet
    // In a full implementation: const userApps = await getAppDeploymentsByDeveloperId(developer.id);
    const userApps = [];

    return c.json({
      apps: userApps,
      total: userApps.length,
    });
  } catch (error) {
    console.error("Error fetching apps:", error);
    return c.json(
      { error: "Failed to fetch apps", message: error.message },
      500,
    );
  }
});

// Test Provider Selection Algorithm
apps.get("/test-provider", async (c) => {
  const provider = await selectProvider();
  return c.json(provider);
});

// Create App Container
apps.post("/create-app", async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = createAppSchema.parse(body);

    const provider = await selectProvider();
    if (!provider) {
      return c.json({ error: "No provider available with capacity" }, 503);
    }

    const deploymentId = crypto.randomUUID();
    const provisionerRequest = {
      type: validatedData.appType,
      cpu: validatedData.cpu,
      memory: validatedData.memory,
      deploymentId: deploymentId,
    };

    const response = await fetch(`${provider.ip}/create-app`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(provisionerRequest),
    });

    const data = await response.json();

    if (response.ok) {
      console.log("[LOG] App creation response:", data);
      // Insert App Deployment
      await insertAppDeployment({
        id: deploymentId,
        developerId: getCurrentDeveloperId(),
        providerResourceId: provider.resourceId,
        appType: validatedData.appType,
        networkName: `${deploymentId}-network`,
        totalCpu: validatedData.cpu,
        totalMemory: parseInt(validatedData.memory),
        status: "creating",
      });

      // Insert Container Records
      for (const container of data.containers) {
        await insertDeploymentContainers({
          deploymentId: deploymentId,
          containerId: container.id,
          containerType: container.type,
          name: container.id,
          image: container.type,
          cpuCores: container.type === "main" ? validatedData.cpu : 1,
          ram: parseInt(validatedData.memory),
          storage: 12,
          port: container.port,
          status: container.status,
        });
      }

      // Update Deployment Status
      await updateDeploymentContainersStatus(deploymentId, "running");
    }

    return c.json(data, response.status);
  } catch (error) {
    console.error("[ERROR] Create app failed:", error);
    if (error instanceof z.ZodError) {
      return c.json({ error: "Validation error", details: error.errors }, 400);
    }
    return c.json(
      { error: "Internal server error", message: error.message },
      500,
    );
  }
});

// Start Deployment
apps.post("/start-app", async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = deploymentControlSchema.parse(body);

    // Call provisioner to start deployment
    const response = await fetch("http://localhost:8800/start-deployment", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ deploymentId: validatedData.deploymentId }),
    });

    const data = await response.json();

    if (response.ok) {
      // Update Deployment Status
      await updateDeploymentContainersStatus(
        validatedData.deploymentId,
        "running",
      );
    }

    return c.json(data, response.status);
  } catch (error) {
    console.error("[ERROR] Start deployment failed:", error);
    if (error instanceof z.ZodError) {
      return c.json({ error: "Validation error", details: error.errors }, 400);
    }
    return c.json(
      { error: "Internal server error", message: error.message },
      500,
    );
  }
});

// Stop Deployment
apps.post("/stop-app", async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = deploymentControlSchema.parse(body);

    // Call provisioner to stop deployment
    const response = await fetch("http://localhost:8800/stop-deployment", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ deploymentId: validatedData.deploymentId }),
    });

    const data = await response.json();

    if (response.ok) {
      // Update Deployment Status
      await updateDeploymentContainersStatus(
        validatedData.deploymentId,
        "stopped",
      );
    }

    return c.json(data, response.status);
  } catch (error) {
    console.error("[ERROR] Stop deployment failed:", error);
    if (error instanceof z.ZodError) {
      return c.json({ error: "Validation error", details: error.errors }, 400);
    }
    return c.json(
      { error: "Internal server error", message: error.message },
      500,
    );
  }
});

// Delete Deployment
apps.post("/delete-app", async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = deploymentControlSchema.parse(body);

    // Call provisioner to delete deployment
    const response = await fetch("http://localhost:8800/delete-deployment", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ deploymentId: validatedData.deploymentId }),
    });

    const data = await response.json();

    if (response.ok) {
      // Update Deployment Status
      await updateDeploymentContainersStatus(
        validatedData.deploymentId,
        "deleted",
      );
    }

    return c.json(data, response.status);
  } catch (error) {
    console.error("[ERROR] Delete deployment failed:", error);
    if (error instanceof z.ZodError) {
      return c.json({ error: "Validation error", details: error.errors }, 400);
    }
    return c.json(
      { error: "Internal server error", message: error.message },
      500,
    );
  }
});

export { apps };
