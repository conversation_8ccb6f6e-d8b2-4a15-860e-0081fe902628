import { Hono } from "hono";
import {
  getDeveloperVmByContainerId,
  getVMsByDeveloperId,
  insertDeveloperVm,
  updateVmStatus,
} from "../db/queries/vm-deployments-queries.ts";
import {
  insertAppDeployment,
  insertDeploymentContainers,
  updateDeploymentContainersStatus,
} from "../db/queries/apps-deployments-queries.ts";
import { z } from "zod";
import { APP_TYPES } from "../utils/constants.ts";
import {
  getProviderIdByUserId,
  selectProvider,
} from "../db/queries/providers-queries.ts";
import { getVmSshTunnelInfo, getAnActiveBastion } from "../db/queries/bastions-queries.ts";
import { getDeveloperByUserId } from "../db/queries/developers-queries.ts";
import { getVmById, getProviderById, updateVmStatusById, deleteVmById } from "../db/queries/vms-queries.ts";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";
import { db } from "../db/db.ts";
import { providerResources as resourceSchema } from "../drizzle/schema.ts";
import { eq } from "drizzle-orm";

// Request/Response Types
interface CreateVMRequest {
  name: string;
  username: string;
  sshKey: string;
  cpu: number;
  memory: string;
  disk: string;
}

interface CreateAppRequest {
  appType: typeof APP_TYPES[number];
  cpu: number;
  memory: string;
  devSSHKey?: string;
}

interface ContainerControlRequest {
  containerId: string;
  type: "vm" | "app";
}

interface DeploymentControlRequest {
  deploymentId: string;
}

// Validation Schemas
const createVMSchema = z.object({
  name: z.string().min(1).max(100),
  username: z.string().min(1),
  sshKey: z.string().min(1),
  cpu: z.number().min(1).max(8),
  memory: z.string().regex(/^\d+[mg]$/),
  disk: z.string().regex(/^\d+[mg]$/),
});

const createAppSchema = z.object({
  appType: z.enum(APP_TYPES as [string, ...string[]]),
  cpu: z.number().min(1).max(4),
  memory: z.string().regex(/^\d+[mg]$/),
  devSSHKey: z.string().optional(),
});

const containerControlSchema = z.object({
  containerId: z.string().min(1),
  type: z.enum(["vm", "app"]),
});

const deploymentControlSchema = z.object({
  deploymentId: z.string().min(1),
});

const vms = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Test Provider Selection Algorithm (public endpoint for debugging)
vms.get("/test-provider", async (c) => {
  try {
    // Test with the exact same parameters as the failing VM creation
    const testProvider = await selectProvider(2, 4, 80);

    // Also get all ready resources for comparison
    const allReady = await db.select().from(resourceSchema).where(eq(resourceSchema.status, "ready"));
    const allActive = await db.select().from(resourceSchema).where(eq(resourceSchema.status, "active"));
    const allResources = await db.select().from(resourceSchema);

    return c.json({
      testQuery: {
        cpu: 2,
        ram: 4,
        storage: 80,
        result: testProvider
      },
      allReadyResources: allReady,
      allActiveResources: allActive,
      allResources: allResources.map(r => ({
        id: r.id,
        status: r.status,
        cpu: r.cpuCores,
        ram: r.ram,
        storage: r.storage,
        ipAddress: r.ipAddress
      }))
    });
  } catch (error) {
    return c.json({ error: error.message }, 500);
  }
});

// Apply auth middleware to all routes
vms.use("*", authMiddleware);

// Get VMs for the authenticated user
vms.get("/", async (c) => {
  try {
    const user = c.get("user");
    if (!user) {
      return c.json({ error: "User not found" }, 401);
    }

    // Get developer record for this user
    const developer = await getDeveloperByUserId(user.id);
    if (!developer) {
      // If no developer record, return empty array (user hasn't created any VMs yet)
      return c.json({
        vms: [],
        total: 0,
      });
    }

    // Get VMs for this developer
    const userVMs = await getVMsByDeveloperId(developer.id);

    // Enhance VMs with SSH tunnel information
    const enhancedVMs = await Promise.all(
      userVMs.map(async (vm) => {
        try {
          // Get SSH tunnel information if available
          const tunnelInfo = await getVmSshTunnelInfo(vm.containerId);
          if (tunnelInfo && tunnelInfo.length > 0) {
            const tunnel = tunnelInfo[0];

            // Get bastion info to construct SSH access command
            const bastions = await getAnActiveBastion();
            if (bastions && bastions.length > 0) {
              const bastionIp = bastions[0].ip;

              return {
                ...vm,
                sshTunnel: {
                  bastionIp,
                  bastionPort: tunnel.assignedPort,
                  sshAccess: `ssh ${tunnel.sshUsername}@${bastionIp} -p ${tunnel.assignedPort}`,
                  status: tunnel.status,
                }
              };
            }
          }

          return vm;
        } catch (tunnelError) {
          console.warn(`⚠️  Could not get SSH tunnel info for VM ${vm.containerId}:`, tunnelError.message);
          return vm;
        }
      })
    );

    return c.json({
      vms: enhancedVMs,
      total: enhancedVMs.length,
    });
  } catch (error) {
    console.error("Error fetching VMs:", error);
    return c.json(
      { error: "Failed to fetch VMs", message: error.message },
      500,
    );
  }
});



// Debug endpoint for VM creation resources
vms.get("/debug-resources", async (c) => {
  try {
    const user = c.get("user");
    if (!user) {
      return c.json({ error: "User not found" }, 401);
    }

    // Get all resources that could be used for VM creation
    const allResources = await selectProvider(1, 1, 1); // Get all available resources

    return c.json({
      message: "VM creation resource debug information",
      totalResources: allResources?.length || 0,
      resources: allResources?.map(r => ({
        id: r.id,
        providerId: r.providerId,
        ipAddress: r.ipAddress, // This should be bastion:port format
        cpu: r.cpuCores,
        ram: r.ram,
        storage: r.storage,
        status: "ready" // Resources returned by selectProvider are always ready
      })) || [],
      note: "ipAddress should be in format 'bastion_ip:port' for tunnel communication"
    });
  } catch (error) {
    console.error("Error in debug-resources:", error);
    return c.json({ error: "Failed to get debug info", message: error.message }, 500);
  }
});

// Create Ubuntu VM
vms.post("/create-vm", async (c) => {
  try {
    const user = c.get("user");
    if (!user) {
      return c.json({ error: "User not found" }, 401);
    }

    // Get developer record
    const developer = await getDeveloperByUserId(user.id);
    if (!developer) {
      return c.json({
        error: "Developer record not found. Please complete your profile.",
      }, 404);
    }

    // Check if developer has SSH key
    if (!developer.sshPublicKey || developer.sshPublicKey.trim().length === 0) {
      return c.json({
        error: "SSH public key required",
        message:
          "Please add your SSH public key in your profile before creating VMs.",
      }, 400);
    }

    const body = await c.req.json();
    const validatedData = createVMSchema.parse(body);

    // Use the SSH key from the developer's profile instead of the request
    const vmData = {
      ...validatedData,
      sshKey: developer.sshPublicKey,
    };

    // Extract resource requirements from validated data
    const cpuRequired = validatedData.cpu;
    const memoryRequired = parseInt(validatedData.memory); // Convert "4g" to 4
    const diskRequired = parseInt(validatedData.disk); // Convert "80g" to 80

    console.log(`🔍 Looking for provider with: CPU=${cpuRequired}, RAM=${memoryRequired}GB, Storage=${diskRequired}GB`);

    const providerResult = await selectProvider(
      cpuRequired,
      memoryRequired,
      diskRequired,
    );

    console.log(`📊 Provider search result:`, providerResult);

    if (!providerResult || providerResult.length === 0) {
      return c.json({
        error: "No provider available with capacity",
        message:
          `No provider found with ${cpuRequired} CPU, ${memoryRequired}GB RAM, ${diskRequired}GB storage`,
        debug: {
          requested: { cpu: cpuRequired, ram: memoryRequired, storage: diskRequired },
          parsedFromFrontend: {
            memory: validatedData.memory,
            disk: validatedData.disk
          }
        }
      }, 503);
    }

    const provider = providerResult[0]; // Get first available provider

    try {
      // provider.ipAddress is in format "bastion_ip:port" (e.g., "*************:7777")
      // We need to connect to the agent through this tunnel endpoint
      const agentUrl = `http://${provider.ipAddress}/create-vm`;

      console.log(`🔗 Connecting to agent at: ${agentUrl}`);
      console.log(`📦 Sending VM data:`, JSON.stringify(vmData, null, 2));

      const response = await fetch(agentUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(vmData),
        // Increased timeout for Docker operations (image pulling can take time)
        signal: AbortSignal.timeout(300000), // 5 minute timeout
      });

      console.log(`📡 Agent response status: ${response.status}`);

      const data = await response.json();
      console.log(`📦 Agent response data:`, JSON.stringify(data, null, 2));

      if (response.ok) {
        console.log(`✅ VM creation successful, inserting into database`);

        await insertDeveloperVm({
          developerId: developer.id,
          providerResourceId: provider.id,
          containerId: data.vmId,
          name: validatedData.name,
          ram: memoryRequired,
          cpuCores: cpuRequired,
          storage: diskRequired,
          status: data.status || "creating",
          sshPublicKey: developer.sshPublicKey,
          sshPort: data.sshPort,
        });

        console.log(`💾 VM record saved to database`);

        // Get SSH tunnel information if available
        try {
          const tunnelInfo = await getVmSshTunnelInfo(data.vmId);
          if (tunnelInfo && tunnelInfo.length > 0) {
            const tunnel = tunnelInfo[0];
            const bastionIp = provider.ipAddress.split(':')[0];

            // Smart validation: Check if tunnel is actually reachable
            let tunnelStatus = tunnel.status;
            let sshAccess = `ssh ${tunnel.sshUsername}@${bastionIp} -p ${tunnel.assignedPort}`;

            // Perform lightweight health check for new tunnels
            if (tunnel.status === 'active') {
              try {
                const healthResponse = await fetch(`${process.env.KUMULUS_API_URL || 'http://localhost:8000'}/api/temp/validate-tunnel-health`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    bastionIp,
                    port: tunnel.assignedPort,
                    tunnelId: tunnel.id,
                  }),
                });

                if (healthResponse.ok) {
                  const healthData = await healthResponse.json();
                  if (!healthData.isHealthy) {
                    tunnelStatus = 'inactive';
                    sshAccess = `# Tunnel currently unavailable - ${sshAccess}`;
                  }
                }
              } catch (healthError) {
                console.warn(`⚠️  Tunnel health check failed:`, healthError.message);
                // Don't change status if health check fails
              }
            }

            data.sshTunnel = {
              bastionIp,
              bastionPort: tunnel.assignedPort,
              sshAccess,
              status: tunnelStatus,
              realTimeStatus: tunnelStatus === 'active' ? 'verified' : 'unverified'
            };
            console.log(`🔗 SSH tunnel info added to response (status: ${tunnelStatus})`);
          }
        } catch (tunnelError) {
          console.warn(`⚠️  Could not get SSH tunnel info:`, tunnelError.message);
          // Don't fail the entire request if tunnel info is unavailable
        }
      } else {
        console.error(`❌ Agent returned error: ${response.status}`, data);
      }

      return c.json(data, response.status);
    } catch (providerError) {
      console.error(`❌ Failed to connect to agent:`, providerError);

      // Check if it's a network/connection error
      if (providerError.name === 'TypeError' || providerError.name === 'AbortError') {
        console.error(`🔌 Connection failed to agent at: http://${provider.ipAddress}/create-vm`);
        console.error(`💡 This could mean:`);
        console.error(`   - Agent is not running on the provider machine`);
        console.error(`   - SSH tunnel is not established`);
        console.error(`   - Bastion is not forwarding traffic correctly`);
        console.error(`   - Network connectivity issues`);

        return c.json({
          error: "Failed to connect to provider agent",
          message: `Could not reach agent at ${provider.ipAddress}. Please check if the agent is running and the tunnel is established.`,
          details: {
            agentUrl: `http://${provider.ipAddress}/create-vm`,
            errorType: providerError.name,
            errorMessage: providerError.message
          }
        }, 503);
      }

      // For other errors, still provide mock response for development
      console.log(
        "⚠️  Provider service error, returning mock response for development",
      );

      const mockVmId = `vm-${Date.now()}`;
      const mockSshPort = 22000 + Math.floor(Math.random() * 1000);

      // Insert VM record with mock data
      await insertDeveloperVm({
        developerId: developer.id,
        providerResourceId: provider.id,
        containerId: mockVmId,
        name: validatedData.name,
        ram: memoryRequired,
        cpuCores: cpuRequired,
        storage: diskRequired,
        status: "creating",
        sshPublicKey: developer.sshPublicKey,
        sshPort: mockSshPort,
      });

      return c.json({
        success: true,
        vmId: mockVmId,
        status: "creating",
        sshPort: mockSshPort,
        message: "VM creation initiated (development mode - agent connection failed)",
        ipAddress: provider.ipAddress.split(':')[0], // Extract IP from bastion:port
        debug: {
          originalError: providerError.message,
          agentUrl: `http://${provider.ipAddress}/create-vm`
        }
      });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return c.json({ error: "Validation error", details: error.errors }, 400);
    }
    return c.json(
      { error: "Internal server error", message: error.message },
      500,
    );
  }
});

// Start VM by containerId
vms.post("/start-vm", async (c) => {
  try {
    const body = await c.req.json();
    const vmId = body.vmId;

    // Check VM status in database first
    const vm = await getDeveloperVmByContainerId(vmId);
    if (!vm) {
      return c.json({ error: "VM not found" }, 404);
    }
    if (vm.status === "deleted") {
      return c.json({ error: "Cannot start a deleted VM" }, 400);
    }
    if (vm.status === "running") {
      return c.json({ error: "VM is already running" }, 400);
    }

    const response = await fetch("http://localhost:8800/start-vm", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ vmId }),
    });

    const data = await response.json();

    if (response.ok) {
      await updateVmStatus(vmId, "running");
      return c.json(data, response.status);
    } else if (response.status === 404) {
      // If VM not found, update status to deleted
      await updateVmStatus(vmId, "deleted");
      return c.json(data, response.status);
    } else {
      return c.json(data, response.status);
    }
  } catch (error) {
    return c.json(
      { error: "Internal server error", message: error.message },
      500,
    );
  }
});

// Stop VM by containerId
vms.post("/stop-vm", async (c) => {
  try {
    const body = await c.req.json();
    const vmId = body.vmId;

    // Check VM status in database first
    const vm = await getDeveloperVmByContainerId(vmId);
    if (!vm) {
      return c.json({ error: "VM not found" }, 404);
    }
    if (vm.status === "deleted") {
      return c.json({ error: "Cannot stop a deleted VM" }, 400);
    }
    if (vm.status === "stopped") {
      return c.json({ error: "VM is already stopped" }, 400);
    }

    const response = await fetch("http://localhost:8800/stop-vm", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ vmId }),
    });

    const data = await response.json();

    if (response.ok) {
      await updateVmStatus(vmId, "stopped");
      return c.json(data, response.status);
    } else if (response.status === 404) {
      // If VM not found, update status to deleted
      await updateVmStatus(vmId, "deleted");
      return c.json(data, response.status);
    } else {
      return c.json(data, response.status);
    }
  } catch (error) {
    return c.json(
      { error: "Internal server error", message: error.message },
      500,
    );
  }
});

// Delete VM by containerId
vms.post("/delete-vm", async (c) => {
  try {
    const body = await c.req.json();
    const vmId = body.vmId;

    // Check VM status in database first
    const vm = await getDeveloperVmByContainerId(vmId);
    if (!vm) {
      return c.json({ error: "VM not found" }, 404);
    }
    if (vm.status === "deleted") {
      return c.json({ error: "VM is already deleted" }, 400);
    }

    const response = await fetch("http://localhost:8800/delete-vm", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ vmId }),
    });

    const data = await response.json();

    if (response.ok || response.status === 404) {
      // Update status to deleted regardless of whether the container exists
      await updateVmStatus(vmId, "deleted");
      return c.json(data, response.status);
    } else {
      return c.json(data, response.status);
    }
  } catch (error) {
    return c.json(
      { error: "Internal server error", message: error.message },
      500,
    );
  }
});

// VM Status Update endpoint (called by agents)
vms.post("/update-status", async (c) => {
  try {
    const body = await c.req.json();
    const { vmId, status, message } = body;

    if (!vmId || !status) {
      return c.json({ error: "vmId and status are required" }, 400);
    }

    console.log(`🔄 Updating VM status: ${vmId} -> ${status}`);

    // Update VM status in database
    await updateVmStatusById(vmId, status);

    console.log(`✅ VM status updated successfully`);

    return c.json({
      success: true,
      vmId,
      status,
      message: message || "Status updated successfully"
    });

  } catch (error) {
    console.error("Error updating VM status:", error);
    return c.json({
      error: "Failed to update VM status",
      message: error.message
    }, 500);
  }
});

// Start VM endpoint
vms.post("/start", async (c) => {
  try {
    const body = await c.req.json();
    const { vmId } = body;

    if (!vmId) {
      return c.json({ error: "vmId is required" }, 400);
    }

    console.log(`🚀 Starting VM: ${vmId}`);

    // Get VM details using containerId (vmId is actually containerId from frontend)
    const vm = await getDeveloperVmByContainerId(vmId);
    if (!vm) {
      return c.json({ error: "VM not found" }, 404);
    }

    // Check current status
    if (vm.status === "running") {
      return c.json({ error: "VM is already running" }, 400);
    }
    if (vm.status === "starting") {
      return c.json({ error: "VM is already starting" }, 400);
    }

    // Set status to starting
    await updateVmStatus(vmId, "starting");

    // Get provider details
    const provider = await db.select().from(resourceSchema).where(
      eq(resourceSchema.id, vm.providerResourceId)
    ).limit(1);

    if (!provider || provider.length === 0) {
      await updateVmStatus(vmId, "error");
      return c.json({ error: "Provider not found" }, 404);
    }

    try {
      // Send start command to agent
      const agentUrl = `http://${provider[0].ipAddress}/start-vm`;
      const response = await fetch(agentUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ vmId }),
        signal: AbortSignal.timeout(30000),
      });

      if (!response.ok) {
        throw new Error(`Agent responded with status ${response.status}`);
      }

      const result = await response.json();

      // Update VM status to running
      await updateVmStatus(vmId, "running");

      console.log(`✅ VM started successfully: ${vmId}`);

      return c.json({
        success: true,
        vmId,
        status: "running",
        message: "VM started successfully"
      });

    } catch (agentError) {
      console.error("Error communicating with agent:", agentError);
      // Set status back to stopped on failure
      await updateVmStatus(vmId, "stopped");

      return c.json({
        error: "Failed to start VM",
        message: "Could not communicate with provider agent"
      }, 503);
    }

  } catch (error) {
    console.error("Error starting VM:", error);
    return c.json({
      error: "Failed to start VM",
      message: error.message
    }, 500);
  }
});

// Stop VM endpoint
vms.post("/stop", async (c) => {
  try {
    const body = await c.req.json();
    const { vmId } = body;

    if (!vmId) {
      return c.json({ error: "vmId is required" }, 400);
    }

    console.log(`🛑 Stopping VM: ${vmId}`);

    // Get VM details using containerId (vmId is actually containerId from frontend)
    const vm = await getDeveloperVmByContainerId(vmId);
    if (!vm) {
      return c.json({ error: "VM not found" }, 404);
    }

    // Check current status
    if (vm.status === "stopped") {
      return c.json({ error: "VM is already stopped" }, 400);
    }
    if (vm.status === "stopping") {
      return c.json({ error: "VM is already stopping" }, 400);
    }

    // Set status to stopping
    await updateVmStatus(vmId, "stopping");

    // Get provider details
    const provider = await db.select().from(resourceSchema).where(
      eq(resourceSchema.id, vm.providerResourceId)
    ).limit(1);

    if (!provider || provider.length === 0) {
      await updateVmStatus(vmId, "error");
      return c.json({ error: "Provider not found" }, 404);
    }

    try {
      // Send stop command to agent
      const agentUrl = `http://${provider[0].ipAddress}/stop-vm`;
      const response = await fetch(agentUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ vmId }),
        signal: AbortSignal.timeout(30000),
      });

      if (!response.ok) {
        throw new Error(`Agent responded with status ${response.status}`);
      }

      const result = await response.json();

      // Update VM status to stopped
      await updateVmStatus(vmId, "stopped");

      console.log(`✅ VM stopped successfully: ${vmId}`);

      return c.json({
        success: true,
        vmId,
        status: "stopped",
        message: "VM stopped successfully"
      });

    } catch (agentError) {
      console.error("Error communicating with agent:", agentError);
      // Set status back to running on failure
      await updateVmStatus(vmId, "running");

      return c.json({
        error: "Failed to stop VM",
        message: "Could not communicate with provider agent"
      }, 503);
    }

  } catch (error) {
    console.error("Error stopping VM:", error);
    return c.json({
      error: "Failed to stop VM",
      message: error.message
    }, 500);
  }
});

// Delete VM endpoint
vms.post("/delete", async (c) => {
  try {
    const body = await c.req.json();
    const { vmId } = body;

    if (!vmId) {
      return c.json({ error: "vmId is required" }, 400);
    }

    console.log(`🗑️ Deleting VM: ${vmId}`);

    // Get VM details
    const vm = await getVmById(vmId);
    if (!vm) {
      return c.json({ error: "VM not found" }, 404);
    }

    // Get provider details
    const provider = await getProviderById(vm.providerId);
    if (!provider) {
      return c.json({ error: "Provider not found" }, 404);
    }

    // Send delete command to agent
    const agentUrl = `http://${provider.ipAddress}/delete-vm`;
    const response = await fetch(agentUrl, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ vmId: vm.vmId }),
      signal: AbortSignal.timeout(30000),
    });

    if (!response.ok) {
      throw new Error(`Agent responded with status ${response.status}`);
    }

    const result = await response.json();

    // Delete VM from database
    await deleteVmById(vmId);

    console.log(`✅ VM deleted successfully: ${vmId}`);

    return c.json({
      success: true,
      vmId,
      message: "VM deleted successfully"
    });

  } catch (error) {
    console.error("Error deleting VM:", error);
    return c.json({
      error: "Failed to delete VM",
      message: error.message
    }, 500);
  }
});

export { vms };
