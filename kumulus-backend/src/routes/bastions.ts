import { Hono } from "hono";
import { db } from "../db/db.ts";
import { bastions as bastionsSchema } from "../drizzle/schema.ts";
import { and, eq } from "drizzle-orm";
import {
  getAnActiveBastion,
  registerBastion,
} from "../db/queries/bastions-queries.ts";

const bastions = new Hono();

const BASTION_PORT_RANGE_START = 18000;
const BASTION_PORT_RANGE_END = 28000;
const BASTION_PORT_BLOCK_SIZE = 1000;

//find a free port block
async function findFreePortBlock(
  desiredSize: number,
  globalStart: number,
  globalEnd: number,
): Promise<{ start: number; end: number } | null> {
  const assignedRanges = await db.select({
    start: bastionsSchema.portRangeStart,
    end: bastionsSchema.portRangeEnd,
  })
    .from(bastionsSchema)
    .where(eq(bastionsSchema.isActive, true))
    .orderBy(bastionsSchema.portRangeStart);

  let currentPointer = globalStart;

  for (const range of assignedRanges) {
    if (currentPointer + desiredSize - 1 < range.start) {
      return { start: currentPointer, end: currentPointer + desiredSize - 1 };
    }
    currentPointer = Math.max(currentPointer, range.end + 1);
  }

  if (currentPointer + desiredSize - 1 <= globalEnd) {
    return { start: currentPointer, end: currentPointer + desiredSize - 1 };
  }

  return null;
}

bastions.get("/", async (c) => {
  const allBastions = await db.select().from(bastionsSchema);
  return c.json(allBastions);
});

// Register a bastion node
bastions.post("/register", async (c) => {
  const body = await c.req.json();
  const { name, ip_address, ssh_public_key } = body;

  if (!name || !ip_address) {
    return c.json({ ok: false, error: "Missing required fields" }, 400);
  }

  const portRange = await findFreePortBlock(
    BASTION_PORT_BLOCK_SIZE,
    BASTION_PORT_RANGE_START,
    BASTION_PORT_RANGE_END,
  );
  if (!portRange) {
    return c.json({ ok: false, error: "No free port range available" }, 503);
  }

  try {
    await registerBastion(
      name,
      ip_address,
      portRange.start,
      portRange.end,
      ssh_public_key,
    );
    return c.json({
      ok: true,
      message: "Bastion registered successfully",
      port_range: portRange,
    });
  } catch (err) {
    return c.json({ ok: false, error: "Failed to register bastion" }, 500);
  }
});

// Get bastion configuration for resource setup
bastions.get("/config", async (c) => {
  try {
    // Get an active bastion with SSH public key
    const activeBastion = await getAnActiveBastion();
    if (!activeBastion || activeBastion.length === 0) {
      return c.json({ error: "No active bastion available" }, 503);
    }

    const bastionInfo = activeBastion[0];

    // Get bastion configuration from database
    const bastionConfig = {
      address: bastionInfo.ip,
      port: 7777, // Default port for agent connections (simple, no dynamic allocation)
      publicKey: bastionInfo.sshPublicKey || "",
    };

    return c.json({
      message: "Bastion configuration",
      config: bastionConfig,
    });
  } catch (error) {
    console.error("Error fetching bastion config:", error);
    return c.json({ error: "Failed to fetch bastion configuration" }, 500);
  }
});

export { bastions };
