import { Hono } from "hono";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";
import {
  getResourceById,
  getResourcesByProviderId,
  registerResource,
  updateResource,
} from "../db/queries/resources-queries.ts";
import { getProviderIdByUserId } from "../db/queries/providers-queries.ts";
import { zResourceSchema } from "../utils/zod.ts";
import {
  getAnActiveBastion,
  getAgentPort,
  reserveTunnelPort
} from "../db/queries/bastions-queries.ts";

// Resource status interface to match frontend expectations
interface ResourceStatus {
  id: string;
  resource_name: string;
  cpu_cores_offered: number;
  ram_gb_offered: number;
  storage_gb_offered: number;
  gpu_available: boolean;
  gpu_count?: number;
  gpu_vram_gb?: number;
  status: "pending" | "installing" | "ready" | "active" | "inactive" | "error";
  createdAt: string;
  updatedAt: string;
  installationProgress?: number;
  lastHeartbeat?: string;
  errorMessage?: string;
}

const resources = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Apply auth middleware to all routes in this router
resources.use("*", authMiddleware);

// Register a resource by a provider
resources.post("/register", async (c) => {
  try {
    // Parse the request body
    const body = await c.req.json();

    // Validate the input
    const validationResult = zResourceSchema.safeParse(body);

    if (!validationResult.success) {
      console.error("Validation error:", validationResult.error);
      return c.json(validationResult.error);
    }

    const validatedData = validationResult.data;

    // Retrieve User ID from session
    const user = c.get("user");
    if (!user) {
      return c.json({ error: "Unauthorized" }, 401);
    }
    const userId = user.id;

    // TODO: Remove later and pass data from the frontend using context or something else
    // Retrieve provider ID from user ID
    const providerId = (await getProviderIdByUserId(userId)).at(0).id;

    //TODO: Assign bastion and agent port
    const bastionInfos = (await getAnActiveBastion()).at(0);
    console.log("🟢 Bastion Infos", bastionInfos);
    

    // Get bastion configuration and assign unique agent port BEFORE creating resource
    const activeBastion = await getAnActiveBastion();
    if (!activeBastion || activeBastion.length === 0) {
      return c.json({ error: "No active bastion available" }, 503);
    }

    const bastionInfo = activeBastion[0];
    const assignedAgentPort = await getAgentPort(bastionInfo.id);

    // Create the bastion tunnel endpoint that VMs will connect to
    const bastionTunnelEndpoint = `${bastionInfo.ip}:${assignedAgentPort}`;

    // Insert the resource into the database with the bastion tunnel endpoint
    const result = await registerResource(
      validatedData.name,
      providerId,
      validatedData.cpuCores,
      validatedData.ram,
      validatedData.storage,
      validatedData.gpu,
      bastionTunnelEndpoint, // Store bastion endpoint, not provider IP
    );

    // CRITICAL: Immediately reserve the tunnel port to prevent race conditions
    const resourceId = result.at(0)?.id;
    if (resourceId) {
      await reserveTunnelPort(bastionInfo.id, resourceId, assignedAgentPort);
      console.log(`🔒 Reserved port ${assignedAgentPort} for resource ${resourceId} on bastion ${bastionInfo.id}`);
    }

    const bastionConfig = {
      address: bastionInfo.ip,
      port: assignedAgentPort, // Unique port for this resource
      publicKey: bastionInfo.sshPublicKey || "",
    };

    return c.json({
      message: "Resource registered successfully",
      data: {
        resourceId: result.at(0)?.id,
        bastionConfig,
      },
    }, 201);
  } catch (error) {
    console.error("Error registering resource:", error);
    return c.json({ error: "Failed to register resource" }, 500);
  }
});

// Get list of ressources for a provider
resources.get("/", async (c) => {
  try {
    // Retrieve User ID from session
    const user = c.get("user");
    if (!user) {
      return c.json({ error: "Unauthorized" }, 401);
    }
    const userId = user.id;

    // Retrieve provider ID from user ID
    const providerId = (await getProviderIdByUserId(userId)).at(0).id;

    // Retrieve resources for this provider
    const resources = await getResourcesByProviderId(providerId);

    return c.json({ resources: resources, total: resources.length });
  } catch (error) {
    console.error("Error fetching resources:", error);
    return c.json({ error: "Failed to fetch resources" }, 500);
  }
});

// Get all resources for the authenticated user
resources.get("/", async (c) => {
  try {
    const user = c.get("user");
    if (!user) {
      return c.json({ error: "User not found" }, 401);
    }

    // Get provider ID for the user
    const providerId = await getProviderIdByUserId(user.id);
    if (!providerId) {
      return c.json({
        message: "User resources",
        resources: [],
      });
    }

    // Get all resources for this provider
    const resources = await getResourcesByProviderId(providerId);

    // Transform to match frontend expectations
    const transformedResources: ResourceStatus[] = resources.map(
      (resource) => ({
        id: resource.id,
        resource_name: resource.name,
        cpu_cores_offered: resource.cpuCores,
        ram_gb_offered: resource.ram,
        storage_gb_offered: resource.storage,
        gpu_available: resource.gpu ? true : false,
        gpu_count: resource.gpu?.count || 0,
        gpu_vram_gb: resource.gpu?.vram || 0,
        status: determineResourceStatus(resource),
        created_at: resource.createdAt?.toISOString() ||
          new Date().toISOString(),
        updated_at: resource.updatedAt?.toISOString() ||
          new Date().toISOString(),
        installationProgress: getInstallationProgress(resource),
        lastHeartbeat: resource.lastHeartbeat?.toISOString(),
        errorMessage: resource.errorMessage,
      }),
    );

    return c.json({
      message: "User resources",
      resources: transformedResources,
    });
  } catch (error) {
    console.error("Error fetching resources:", error);
    return c.json({ error: "Failed to fetch resources" }, 500);
  }
});

// Get specific resource by ID
resources.get("/:id", async (c) => {
  try {
    const user = c.get("user");
    const resourceId = c.req.param("id");

    if (!user) {
      return c.json({ error: "User not found" }, 401);
    }

    const resourceResult = await getResourceById(resourceId);
    if (!resourceResult || resourceResult.length === 0) {
      return c.json({ error: "Resource not found" }, 404);
    }

    const resource = resourceResult[0];

    // Get bastion configuration from database
    const activeBastion = await getAnActiveBastion();
    const bastionConfig = activeBastion && activeBastion.length > 0
      ? {
        address: activeBastion[0].ip,
        port: 7777, // Default port for agent connections
        publicKey: activeBastion[0].sshPublicKey || "",
      }
      : null;

    // Transform to match frontend expectations
    const gpu = resource.gpu as any;
    const transformedResource = {
      id: resource.id,
      name: resource.name,
      cpu: resource.cpuCores,
      ram: resource.ram,
      storage: resource.storage,
      gpu: gpu
        ? {
          count: gpu.count || 0,
          vram: gpu.vram || 0,
        }
        : undefined,
      status: resource.status || "pending",
      createdAt: resource.createdAt || new Date().toISOString(),
      updatedAt: resource.updatedAt || new Date().toISOString(),
      checkedSpecs: resource.checkedSpecs,
      bastionConfig,
    };

    return c.json(transformedResource);
  } catch (error) {
    console.error("Error fetching resource:", error);
    return c.json({ error: "Failed to fetch resource" }, 500);
  }
});

// Update resource status (for installation progress, etc.)
resources.put("/:id", async (c) => {
  try {
    const user = c.get("user");
    const resourceId = c.req.param("id");
    const body = await c.req.json();

    if (!user) {
      return c.json({ error: "User not found" }, 401);
    }

    // Update the resource
    await updateResource(resourceId, body);

    // Return updated resource
    const resourceResult = await getResourceById(resourceId);
    if (!resourceResult || resourceResult.length === 0) {
      return c.json({ error: "Resource not found" }, 404);
    }

    const resource = resourceResult[0];
    const transformedResource: ResourceStatus = {
      id: resource.id,
      resource_name: resource.name,
      cpu_cores_offered: resource.cpuCores,
      ram_gb_offered: resource.ram,
      storage_gb_offered: resource.storage,
      gpu_available: resource.gpu ? true : false,
      gpu_count: resource.gpu?.count || 0,
      gpu_vram_gb: resource.gpu?.vram || 0,
      status: determineResourceStatus(resource),
      createdAt: resource.createdAt || new Date().toISOString(),
      updatedAt: resource.updatedAt || new Date().toISOString(),
      installationProgress: getInstallationProgress(resource),
      lastHeartbeat: resource.lastHeartbeat?.toISOString(),
      errorMessage: resource.errorMessage,
    };

    return c.json({
      message: "Resource updated successfully",
      resource: transformedResource,
    });
  } catch (error) {
    console.error("Error updating resource:", error);
    return c.json({ error: "Failed to update resource" }, 500);
  }
});

// Assign Resource Bastion address and port for agent
// Done at ....
resources.put("/assign-bastion/:resource_id", async (c) => {
  try {
    const resourceId = c.req.param("resource_id");
    const { id, ip } = (await getRandomActiveBastion())[0];
    const agentPort = await getAgentPort(id);

    const fullAddress = `https://${ip}:${agentPort}`;

    // Update the resource
    await updateResource(resourceId, { ipAddress: fullAddress });

    return c.json({
      message: "Bastion assigned successfully",
    });
  } catch (error) {
    console.error("Error assigning bastion:", error);
    return c.json({ error: "Failed to assign bastion" }, 500);
  }
});

// Helper functions
function determineResourceStatus(resource: any): ResourceStatus["status"] {
  // Logic to determine status based on resource data
  if (resource.errorMessage) return "error";
  if (resource.lastHeartbeat) {
    const lastSeen = new Date(resource.lastHeartbeat).getTime();
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;

    if (now - lastSeen > fiveMinutes) return "inactive";
    return "active";
  }
  if (resource.installationProgress === 100) return "ready";
  if (resource.installationProgress > 0) return "installing";
  return "pending";
}

function getInstallationProgress(resource: any): number {
  return resource.installationProgress || 0;
}

// Debug endpoint to check all resources and port assignments
resources.get("/debug", async (c) => {
  try {
    const user = c.get("user");
    if (!user) {
      return c.json({ error: "User not found" }, 401);
    }

    // Get all resources from database
    const allResources = await db.select().from(providerResources);

    // Get bastion info for context
    const bastions = await getAnActiveBastion();

    return c.json({
      message: "Resource and bastion debug information",
      totalResources: allResources.length,
      activeBastions: bastions.length,
      bastionInfo: bastions.map(b => ({
        id: b.id,
        ip: b.ip,
        portRangeStart: b.portRangeStart,
        portRangeEnd: b.portRangeEnd
      })),
      resources: allResources.map(r => ({
        id: r.id,
        name: r.name,
        status: r.status,
        ipAddress: r.ipAddress, // Should be bastion:port format
        cpu: r.cpuCores,
        ram: r.ram,
        storage: r.storage,
        createdAt: r.createdAt,
        updatedAt: r.updatedAt
      }))
    });
  } catch (error) {
    console.error("Error fetching debug resources:", error);
    return c.json({ error: "Failed to fetch debug resources" }, 500);
  }
});

export { resources };
