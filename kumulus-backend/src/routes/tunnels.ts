import { Hono } from "hono";
import { authMiddleware } from "../middleware/auth.ts";
import type { AuthType } from "../utils/auth.ts";
import {
  deleteTunnel,
  getTunnelById,
  getTunnelsByBastionId,
  getTunnelsByStatus,
  insertTunnel,
  updateTunnel,
  updateTunnelStatus,
} from "../db/queries/tunnels-queries.ts";
import { getAnActiveBastion } from "../db/queries/bastions-queries.ts";
import { TunnelManager } from "../services/tunnel-manager.ts";

const tunnels = new Hono<{ Variables: AuthType }>({
  strict: false,
});

// Apply auth middleware to protected routes
tunnels.use("/request", authMiddleware);
tunnels.use("/*/health", authMiddleware);

// Initialize tunnel manager
const tunnelManager = new TunnelManager();

// Request tunnel creation
tunnels.post("/request", async (c) => {
  try {
    const body = await c.req.json();
    const {
      providerContainerId,
      sshUsername,
      sshPublicKey,
      tunnelType = "agent",
      preferredBastionId,
    } = body;

    if (!providerContainerId || !sshUsername || !sshPublicKey) {
      return c.json({
        error:
          "Missing required fields: providerContainerId, sshUsername, sshPublicKey",
      }, 400);
    }

    // Request tunnel creation through tunnel manager
    const result = await tunnelManager.requestTunnel({
      providerContainerId,
      sshUsername,
      sshPublicKey,
      tunnelType,
      preferredBastionId,
    });

    if (!result.success) {
      return c.json({ error: result.error }, result.statusCode || 500);
    }

    return c.json({
      message: "Tunnel requested successfully",
      tunnel: result.tunnel,
      bastionInfo: result.bastionInfo,
    }, 201);
  } catch (error) {
    console.error("Error requesting tunnel:", error);
    return c.json({ error: "Failed to request tunnel" }, 500);
  }
});

// Get tunnel by ID
tunnels.get("/:id", async (c) => {
  try {
    const tunnelId = c.req.param("id");
    const tunnel = await getTunnelById(tunnelId);

    if (!tunnel || tunnel.length === 0) {
      return c.json({ error: "Tunnel not found" }, 404);
    }

    return c.json({
      message: "Tunnel retrieved successfully",
      tunnel: tunnel[0],
    });
  } catch (error) {
    console.error("Error retrieving tunnel:", error);
    return c.json({ error: "Failed to retrieve tunnel" }, 500);
  }
});

// Update tunnel status
tunnels.put("/:id/status", async (c) => {
  try {
    const tunnelId = c.req.param("id");
    const body = await c.req.json();
    const { status, errorMessage } = body;

    if (!status) {
      return c.json({ error: "Status is required" }, 400);
    }

    // Validate status values
    const validStatuses = [
      "assigned",
      "connecting",
      "connected",
      "disconnected",
      "failed",
      "terminated",
    ];
    if (!validStatuses.includes(status)) {
      return c.json({
        error: `Invalid status. Must be one of: ${validStatuses.join(", ")}`,
      }, 400);
    }

    await updateTunnelStatus(tunnelId, status, errorMessage);

    return c.json({
      message: "Tunnel status updated successfully",
      tunnelId,
      status,
    });
  } catch (error) {
    console.error("Error updating tunnel status:", error);
    return c.json({ error: "Failed to update tunnel status" }, 500);
  }
});

// Update tunnel health
tunnels.put("/:id/health", async (c) => {
  try {
    const tunnelId = c.req.param("id");
    const body = await c.req.json();
    const { isHealthy, responseTimeMs, errorMessage } = body;

    await tunnelManager.updateTunnelHealth(tunnelId, {
      isHealthy: isHealthy ?? true,
      responseTimeMs,
      errorMessage,
    });

    return c.json({
      message: "Tunnel health updated successfully",
      tunnelId,
    });
  } catch (error) {
    console.error("Error updating tunnel health:", error);
    return c.json({ error: "Failed to update tunnel health" }, 500);
  }
});

// Get tunnels by bastion
tunnels.get("/bastion/:bastionId", async (c) => {
  try {
    const bastionId = c.req.param("bastionId");
    const tunnels = await getTunnelsByBastionId(bastionId);

    return c.json({
      message: "Bastion tunnels retrieved successfully",
      bastionId,
      tunnels,
      count: tunnels.length,
    });
  } catch (error) {
    console.error("Error retrieving bastion tunnels:", error);
    return c.json({ error: "Failed to retrieve bastion tunnels" }, 500);
  }
});

// Get tunnels by status
tunnels.get("/status/:status", async (c) => {
  try {
    const status = c.req.param("status");
    const tunnels = await getTunnelsByStatus(status);

    return c.json({
      message: "Tunnels by status retrieved successfully",
      status,
      tunnels,
      count: tunnels.length,
    });
  } catch (error) {
    console.error("Error retrieving tunnels by status:", error);
    return c.json({ error: "Failed to retrieve tunnels by status" }, 500);
  }
});

// Terminate tunnel
tunnels.delete("/:id", async (c) => {
  try {
    const tunnelId = c.req.param("id");

    const result = await tunnelManager.terminateTunnel(tunnelId);

    if (!result.success) {
      return c.json({ error: result.error }, result.statusCode || 500);
    }

    return c.json({
      message: "Tunnel terminated successfully",
      tunnelId,
    });
  } catch (error) {
    console.error("Error terminating tunnel:", error);
    return c.json({ error: "Failed to terminate tunnel" }, 500);
  }
});

// Health check endpoint for monitoring
tunnels.get("/health/check", async (c) => {
  try {
    const healthStats = await tunnelManager.getHealthStats();

    return c.json({
      message: "Tunnel health stats",
      stats: healthStats,
    });
  } catch (error) {
    console.error("Error getting tunnel health stats:", error);
    return c.json({ error: "Failed to get health stats" }, 500);
  }
});

export { tunnels };
