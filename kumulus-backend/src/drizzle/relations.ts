import { relations } from "drizzle-orm/relations";
import { user, account, developers, appDeployments, providerResources, developerVms, session, providers, bastions, tunnels, userSettings, healthChecks, deploymentContainers } from "./schema";

export const accountRelations = relations(account, ({one}) => ({
	user: one(user, {
		fields: [account.userId],
		references: [user.id]
	}),
}));

export const userRelations = relations(user, ({many}) => ({
	accounts: many(account),
	sessions: many(session),
	userSettings: many(userSettings),
}));

export const appDeploymentsRelations = relations(appDeployments, ({one, many}) => ({
	developer: one(developers, {
		fields: [appDeployments.developerId],
		references: [developers.id]
	}),
	providerResource: one(providerResources, {
		fields: [appDeployments.providerResourceId],
		references: [providerResources.id]
	}),
	deploymentContainers: many(deploymentContainers),
}));

export const developersRelations = relations(developers, ({many}) => ({
	appDeployments: many(appDeployments),
	developerVms: many(developerVms),
}));

export const providerResourcesRelations = relations(providerResources, ({one, many}) => ({
	appDeployments: many(appDeployments),
	developerVms: many(developerVms),
	provider: one(providers, {
		fields: [providerResources.providerId],
		references: [providers.id]
	}),
}));

export const developerVmsRelations = relations(developerVms, ({one}) => ({
	developer: one(developers, {
		fields: [developerVms.developerId],
		references: [developers.id]
	}),
	providerResource: one(providerResources, {
		fields: [developerVms.providerResourceId],
		references: [providerResources.id]
	}),
}));

export const sessionRelations = relations(session, ({one}) => ({
	user: one(user, {
		fields: [session.userId],
		references: [user.id]
	}),
}));

export const providersRelations = relations(providers, ({many}) => ({
	providerResources: many(providerResources),
	healthChecks: many(healthChecks),
}));

export const tunnelsRelations = relations(tunnels, ({one}) => ({
	bastion: one(bastions, {
		fields: [tunnels.bastionId],
		references: [bastions.id]
	}),
}));

export const bastionsRelations = relations(bastions, ({many}) => ({
	tunnels: many(tunnels),
}));

export const userSettingsRelations = relations(userSettings, ({one}) => ({
	user: one(user, {
		fields: [userSettings.userId],
		references: [user.id]
	}),
}));

export const healthChecksRelations = relations(healthChecks, ({one}) => ({
	provider: one(providers, {
		fields: [healthChecks.providerId],
		references: [providers.id]
	}),
}));

export const deploymentContainersRelations = relations(deploymentContainers, ({one}) => ({
	appDeployment: one(appDeployments, {
		fields: [deploymentContainers.deploymentId],
		references: [appDeployments.id]
	}),
}));