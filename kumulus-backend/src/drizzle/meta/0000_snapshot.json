{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"columns": ["email"], "nullsNotDistinct": false, "name": "user_email_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "schemaTo": "public", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"columns": ["token"], "nullsNotDistinct": false, "name": "session_token_unique"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.providers": {"name": "providers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "wallet_address": {"name": "wallet_address", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": false, "default": 50}, "last_lease_at": {"name": "last_lease_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"providers_user_id_key": {"columns": ["user_id"], "nullsNotDistinct": false, "name": "providers_user_id_key"}, "providers_wallet_address_key": {"columns": ["wallet_address"], "nullsNotDistinct": false, "name": "providers_wallet_address_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.provider_resources": {"name": "provider_resources", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "provider_id": {"name": "provider_id", "type": "uuid", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "inet", "primaryKey": false, "notNull": true}, "cpu_cores": {"name": "cpu_cores", "type": "integer", "primaryKey": false, "notNull": true}, "ram": {"name": "ram", "type": "integer", "primaryKey": false, "notNull": true}, "storage": {"name": "storage", "type": "integer", "primaryKey": false, "notNull": true}, "bandwidth": {"name": "bandwidth", "type": "integer", "primaryKey": false, "notNull": true}, "gpu": {"name": "gpu", "type": "jsonb", "primaryKey": false, "notNull": false}, "ssh_public_key": {"name": "ssh_public_key", "type": "text", "primaryKey": false, "notNull": false}, "checked_specs": {"name": "checked_specs", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"provider_resources_provider_id_fkey": {"name": "provider_resources_provider_id_fkey", "tableFrom": "provider_resources", "tableTo": "providers", "schemaTo": "public", "columnsFrom": ["provider_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.health_checks": {"name": "health_checks", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "provider_id": {"name": "provider_id", "type": "uuid", "primaryKey": false, "notNull": true}, "results": {"name": "results", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"health_checks_provider_id_fkey": {"name": "health_checks_provider_id_fkey", "tableFrom": "health_checks", "tableTo": "providers", "schemaTo": "public", "columnsFrom": ["provider_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.developers": {"name": "developers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "wallet_address": {"name": "wallet_address", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"developers_user_id_key": {"columns": ["user_id"], "nullsNotDistinct": false, "name": "developers_user_id_key"}, "developers_wallet_address_key": {"columns": ["wallet_address"], "nullsNotDistinct": false, "name": "developers_wallet_address_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.developer_vms": {"name": "developer_vms", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "developer_id": {"name": "developer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider_resource_id": {"name": "provider_resource_id", "type": "uuid", "primaryKey": false, "notNull": true}, "container_id": {"name": "container_id", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "ram": {"name": "ram", "type": "integer", "primaryKey": false, "notNull": true}, "cpu_cores": {"name": "cpu_cores", "type": "integer", "primaryKey": false, "notNull": true}, "storage": {"name": "storage", "type": "integer", "primaryKey": false, "notNull": true}, "gpu": {"name": "gpu", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'running'"}, "ssh_public_key": {"name": "ssh_public_key", "type": "text", "primaryKey": false, "notNull": true}, "ssh_port": {"name": "ssh_port", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"developer_vms_developer_id_fkey": {"name": "developer_vms_developer_id_fkey", "tableFrom": "developer_vms", "tableTo": "developers", "schemaTo": "public", "columnsFrom": ["developer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "developer_vms_provider_resource_id_fkey": {"name": "developer_vms_provider_resource_id_fkey", "tableFrom": "developer_vms", "tableTo": "provider_resources", "schemaTo": "public", "columnsFrom": ["provider_resource_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"developer_vms_container_id_key": {"columns": ["container_id"], "nullsNotDistinct": false, "name": "developer_vms_container_id_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.app_deployments": {"name": "app_deployments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "developer_id": {"name": "developer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider_resource_id": {"name": "provider_resource_id", "type": "uuid", "primaryKey": false, "notNull": true}, "app_type": {"name": "app_type", "type": "app_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "network_name": {"name": "network_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "deployment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'creating'"}, "total_cpu": {"name": "total_cpu", "type": "integer", "primaryKey": false, "notNull": true}, "total_memory": {"name": "total_memory", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_deployment_developer": {"name": "idx_deployment_developer", "columns": [{"expression": "developer_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_deployment_provider": {"name": "idx_deployment_provider", "columns": [{"expression": "provider_resource_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_deployment_status": {"name": "idx_deployment_status", "columns": [{"expression": "status", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"app_deployments_developer_id_fkey": {"name": "app_deployments_developer_id_fkey", "tableFrom": "app_deployments", "tableTo": "developers", "schemaTo": "public", "columnsFrom": ["developer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "app_deployments_provider_resource_id_fkey": {"name": "app_deployments_provider_resource_id_fkey", "tableFrom": "app_deployments", "tableTo": "provider_resources", "schemaTo": "public", "columnsFrom": ["provider_resource_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.deployment_containers": {"name": "deployment_containers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "deployment_id": {"name": "deployment_id", "type": "uuid", "primaryKey": false, "notNull": true}, "container_id": {"name": "container_id", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "container_type": {"name": "container_type", "type": "container_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "cpu_cores": {"name": "cpu_cores", "type": "integer", "primaryKey": false, "notNull": true}, "ram": {"name": "ram", "type": "integer", "primaryKey": false, "notNull": true}, "storage": {"name": "storage", "type": "integer", "primaryKey": false, "notNull": true}, "gpu": {"name": "gpu", "type": "jsonb", "primaryKey": false, "notNull": false}, "port": {"name": "port", "type": "integer", "primaryKey": false, "notNull": false}, "internal_port": {"name": "internal_port", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'creating'"}, "environment_vars": {"name": "environment_vars", "type": "jsonb", "primaryKey": false, "notNull": false}, "volumes": {"name": "volumes", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_container_deployment": {"name": "idx_container_deployment", "columns": [{"expression": "deployment_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_container_status": {"name": "idx_container_status", "columns": [{"expression": "status", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"deployment_containers_deployment_id_fkey": {"name": "deployment_containers_deployment_id_fkey", "tableFrom": "deployment_containers", "tableTo": "app_deployments", "schemaTo": "public", "columnsFrom": ["deployment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"deployment_containers_container_id_key": {"columns": ["container_id"], "nullsNotDistinct": false, "name": "deployment_containers_container_id_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.bastions": {"name": "bastions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "inet", "primaryKey": false, "notNull": true}, "port_range_start": {"name": "port_range_start", "type": "integer", "primaryKey": false, "notNull": true}, "port_range_end": {"name": "port_range_end", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_heartbeat": {"name": "last_heartbeat", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.tunnels": {"name": "tunnels", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "provider_container_id": {"name": "provider_container_id", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "bastion_id": {"name": "bastion_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_port": {"name": "assigned_port", "type": "integer", "primaryKey": false, "notNull": true}, "ssh_username": {"name": "ssh_username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "ssh_public_key": {"name": "ssh_public_key", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'assigned'"}, "last_health_check": {"name": "last_health_check", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_tunnel_bastion": {"name": "idx_tunnel_bastion", "columns": [{"expression": "bastion_id", "asc": true, "nulls": "last", "opclass": "uuid_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_tunnel_provider_container": {"name": "idx_tunnel_provider_container", "columns": [{"expression": "provider_container_id", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_tunnel_status": {"name": "idx_tunnel_status", "columns": [{"expression": "status", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tunnels_bastion_id_fkey": {"name": "tunnels_bastion_id_fkey", "tableFrom": "tunnels", "tableTo": "bastions", "schemaTo": "public", "columnsFrom": ["bastion_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tunnels_bastion_id_assigned_port_key": {"columns": ["bastion_id", "assigned_port"], "nullsNotDistinct": false, "name": "tunnels_bastion_id_assigned_port_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {"public.app_type": {"name": "app_type", "values": ["odoo", "wordpress", "libreoffice", "nextcloud"], "schema": "public"}, "public.container_type": {"name": "container_type", "values": ["main", "database", "cache", "proxy"], "schema": "public"}, "public.deployment_status": {"name": "deployment_status", "values": ["creating", "running", "stopped", "failed", "deleted"], "schema": "public"}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {"public.deployment_overview": {"name": "deployment_overview", "schema": "public", "columns": {"deployment_id": {"name": "deployment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "app_type": {"name": "app_type", "type": "app_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "deployment_status": {"name": "deployment_status", "type": "deployment_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "network_name": {"name": "network_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "total_cpu": {"name": "total_cpu", "type": "integer", "primaryKey": false, "notNull": false}, "total_memory": {"name": "total_memory", "type": "integer", "primaryKey": false, "notNull": false}, "containers": {"name": "containers", "type": "json", "primaryKey": false, "notNull": false}}, "isExisting": false, "definition": "SELECT d.id AS deployment_id, d.app_type, d.status AS deployment_status, d.network_name, d.total_cpu, d.total_memory, json_agg(json_build_object('container_id', c.container_id, 'container_type', c.container_type, 'status', c.status, 'port', c.port)) AS containers FROM app_deployments d LEFT JOIN deployment_containers c ON d.id = c.deployment_id GROUP BY d.id", "materialized": false}}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}}}