-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TYPE "public"."app_type" AS ENUM('odoo', 'wordpress', 'libreoffice', 'nextcloud');--> statement-breakpoint
CREATE TYPE "public"."container_type" AS ENUM('main', 'database', 'cache', 'proxy');--> statement-breakpoint
CREATE TYPE "public"."deployment_status" AS ENUM('creating', 'running', 'stopped', 'failed', 'deleted');--> statement-breakpoint
CREATE TABLE "verification" (
	"id" text PRIMARY KEY NOT NULL,
	"identifier" text NOT NULL,
	"value" text NOT NULL,
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"email_verified" boolean NOT NULL,
	"image" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	CONSTRAINT "user_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "account" (
	"id" text PRIMARY KEY NOT NULL,
	"account_id" text NOT NULL,
	"provider_id" text NOT NULL,
	"user_id" text NOT NULL,
	"access_token" text,
	"refresh_token" text,
	"id_token" text,
	"access_token_expires_at" timestamp,
	"refresh_token_expires_at" timestamp,
	"scope" text,
	"password" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "session" (
	"id" text PRIMARY KEY NOT NULL,
	"expires_at" timestamp NOT NULL,
	"token" text NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	"ip_address" text,
	"user_agent" text,
	"user_id" text NOT NULL,
	CONSTRAINT "session_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE "providers" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" text NOT NULL,
	"name" varchar(100) NOT NULL,
	"wallet_address" varchar(200) NOT NULL,
	"is_active" boolean DEFAULT false,
	"score" integer DEFAULT 50,
	"last_lease_at" timestamp,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "providers_user_id_key" UNIQUE("user_id"),
	CONSTRAINT "providers_wallet_address_key" UNIQUE("wallet_address")
);
--> statement-breakpoint
CREATE TABLE "provider_resources" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"provider_id" uuid NOT NULL,
	"ip_address" "inet" NOT NULL,
	"cpu_cores" integer NOT NULL,
	"ram" integer NOT NULL,
	"storage" integer NOT NULL,
	"bandwidth" integer NOT NULL,
	"gpu" jsonb,
	"ssh_public_key" text,
	"checked_specs" jsonb,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "health_checks" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"provider_id" uuid NOT NULL,
	"results" jsonb NOT NULL
);
--> statement-breakpoint
CREATE TABLE "developers" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" text NOT NULL,
	"name" varchar(100) NOT NULL,
	"wallet_address" varchar(200) NOT NULL,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "developers_user_id_key" UNIQUE("user_id"),
	CONSTRAINT "developers_wallet_address_key" UNIQUE("wallet_address")
);
--> statement-breakpoint
CREATE TABLE "developer_vms" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"developer_id" uuid NOT NULL,
	"provider_resource_id" uuid NOT NULL,
	"container_id" varchar(200) NOT NULL,
	"ram" integer NOT NULL,
	"cpu_cores" integer NOT NULL,
	"storage" integer NOT NULL,
	"gpu" jsonb,
	"status" varchar(50) DEFAULT 'running' NOT NULL,
	"ssh_public_key" text NOT NULL,
	"ssh_port" integer NOT NULL,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "developer_vms_container_id_key" UNIQUE("container_id")
);
--> statement-breakpoint
CREATE TABLE "app_deployments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"developer_id" uuid NOT NULL,
	"provider_resource_id" uuid NOT NULL,
	"app_type" "app_type" NOT NULL,
	"network_name" varchar(100) NOT NULL,
	"status" "deployment_status" DEFAULT 'creating' NOT NULL,
	"total_cpu" integer NOT NULL,
	"total_memory" integer NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "deployment_containers" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"deployment_id" uuid NOT NULL,
	"container_id" varchar(200) NOT NULL,
	"container_type" "container_type" NOT NULL,
	"name" varchar(100) NOT NULL,
	"image" varchar(200) NOT NULL,
	"cpu_cores" integer NOT NULL,
	"ram" integer NOT NULL,
	"storage" integer NOT NULL,
	"gpu" jsonb,
	"port" integer,
	"internal_port" integer,
	"status" varchar(50) DEFAULT 'creating' NOT NULL,
	"environment_vars" jsonb,
	"volumes" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "deployment_containers_container_id_key" UNIQUE("container_id")
);
--> statement-breakpoint
CREATE TABLE "bastions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	"ip_address" "inet" NOT NULL,
	"port_range_start" integer NOT NULL,
	"port_range_end" integer NOT NULL,
	"is_active" boolean DEFAULT true,
	"last_heartbeat" timestamp,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "tunnels" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"provider_container_id" varchar(200) NOT NULL,
	"bastion_id" uuid NOT NULL,
	"assigned_port" integer NOT NULL,
	"ssh_username" varchar(100),
	"ssh_public_key" text,
	"status" varchar(50) DEFAULT 'assigned' NOT NULL,
	"last_health_check" timestamp,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "tunnels_bastion_id_assigned_port_key" UNIQUE("bastion_id","assigned_port")
);
--> statement-breakpoint
ALTER TABLE "account" ADD CONSTRAINT "account_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "session" ADD CONSTRAINT "session_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "provider_resources" ADD CONSTRAINT "provider_resources_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."providers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "health_checks" ADD CONSTRAINT "health_checks_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."providers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "developer_vms" ADD CONSTRAINT "developer_vms_developer_id_fkey" FOREIGN KEY ("developer_id") REFERENCES "public"."developers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "developer_vms" ADD CONSTRAINT "developer_vms_provider_resource_id_fkey" FOREIGN KEY ("provider_resource_id") REFERENCES "public"."provider_resources"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "app_deployments" ADD CONSTRAINT "app_deployments_developer_id_fkey" FOREIGN KEY ("developer_id") REFERENCES "public"."developers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "app_deployments" ADD CONSTRAINT "app_deployments_provider_resource_id_fkey" FOREIGN KEY ("provider_resource_id") REFERENCES "public"."provider_resources"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "deployment_containers" ADD CONSTRAINT "deployment_containers_deployment_id_fkey" FOREIGN KEY ("deployment_id") REFERENCES "public"."app_deployments"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tunnels" ADD CONSTRAINT "tunnels_bastion_id_fkey" FOREIGN KEY ("bastion_id") REFERENCES "public"."bastions"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_deployment_developer" ON "app_deployments" USING btree ("developer_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_deployment_provider" ON "app_deployments" USING btree ("provider_resource_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_deployment_status" ON "app_deployments" USING btree ("status" enum_ops);--> statement-breakpoint
CREATE INDEX "idx_container_deployment" ON "deployment_containers" USING btree ("deployment_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_container_status" ON "deployment_containers" USING btree ("status" text_ops);--> statement-breakpoint
CREATE INDEX "idx_tunnel_bastion" ON "tunnels" USING btree ("bastion_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_tunnel_provider_container" ON "tunnels" USING btree ("provider_container_id" text_ops);--> statement-breakpoint
CREATE INDEX "idx_tunnel_status" ON "tunnels" USING btree ("status" text_ops);--> statement-breakpoint
CREATE VIEW "public"."deployment_overview" AS (SELECT d.id AS deployment_id, d.app_type, d.status AS deployment_status, d.network_name, d.total_cpu, d.total_memory, json_agg(json_build_object('container_id', c.container_id, 'container_type', c.container_type, 'status', c.status, 'port', c.port)) AS containers FROM app_deployments d LEFT JOIN deployment_containers c ON d.id = c.deployment_id GROUP BY d.id);
*/