CREATE TABLE "user_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" text NOT NULL,
	"notifications" jsonb DEFAULT '{"email":true,"push":false}'::jsonb,
	"privacy" jsonb DEFAULT '{"profileVisible":true,"activityVisible":false}'::jsonb,
	"security" jsonb DEFAULT '{"twoFactorEnabled":false,"lastPasswordChange":null}'::jsonb,
	"preferences" jsonb DEFAULT '{"theme":"dark","language":"en","timezone":"UTC"}'::jsonb,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "user_settings_user_id_unique" UNIQUE("user_id")
);
--> statement-breakpoint
ALTER TABLE "user_settings" ADD CONSTRAINT "user_settings_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;