import { pgTable, unique, text, boolean, timestamp, foreignKey, uuid, varchar, integer, index, jsonb, inet, pgView, json, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const appType = pgEnum("app_type", ['odoo', 'wordpress', 'libreoffice', 'nextcloud'])
export const containerType = pgEnum("container_type", ['main', 'database', 'cache', 'proxy'])
export const deploymentStatus = pgEnum("deployment_status", ['creating', 'running', 'stopped', 'failed', 'deleted'])
export const resourceStatus = pgEnum("resource_status", ['ready', 'active', 'inactive', 'pending', 'installing', 'error'])
export const tunnelType = pgEnum("tunnel_type", ['agent', 'container'])


export const user = pgTable("user", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	email: text().notNull(),
	emailVerified: boolean("email_verified").notNull(),
	image: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).notNull(),
}, (table) => [
	unique("user_email_unique").on(table.email),
]);

export const jwks = pgTable("jwks", {
	id: text().primaryKey().notNull(),
	publicKey: text("public_key").notNull(),
	privateKey: text("private_key").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).notNull(),
});

export const account = pgTable("account", {
	id: text().primaryKey().notNull(),
	accountId: text("account_id").notNull(),
	providerId: text("provider_id").notNull(),
	userId: text("user_id").notNull(),
	accessToken: text("access_token"),
	refreshToken: text("refresh_token"),
	idToken: text("id_token"),
	accessTokenExpiresAt: timestamp("access_token_expires_at", { mode: 'string' }),
	refreshTokenExpiresAt: timestamp("refresh_token_expires_at", { mode: 'string' }),
	scope: text(),
	password: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "account_user_id_user_id_fk"
		}).onDelete("cascade"),
]);

export const providers = pgTable("providers", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	name: varchar({ length: 100 }).notNull(),
	walletAddress: varchar("wallet_address", { length: 200 }).notNull(),
	isActive: boolean("is_active").default(false),
	score: integer().default(50),
	lastLeaseAt: timestamp("last_lease_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	unique("providers_user_id_key").on(table.userId),
	unique("providers_wallet_address_key").on(table.walletAddress),
]);

export const developers = pgTable("developers", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	name: varchar({ length: 100 }).notNull(),
	walletAddress: varchar("wallet_address", { length: 200 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	sshPublicKey: text("ssh_public_key"),
}, (table) => [
	unique("developers_user_id_key").on(table.userId),
	unique("developers_wallet_address_key").on(table.walletAddress),
]);

export const appDeployments = pgTable("app_deployments", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	developerId: uuid("developer_id").notNull(),
	providerResourceId: uuid("provider_resource_id").notNull(),
	appType: appType("app_type").notNull(),
	networkName: varchar("network_name", { length: 100 }).notNull(),
	status: deploymentStatus().default('creating').notNull(),
	totalCpu: integer("total_cpu").notNull(),
	totalMemory: integer("total_memory").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_deployment_developer").using("btree", table.developerId.asc().nullsLast().op("uuid_ops")),
	index("idx_deployment_provider").using("btree", table.providerResourceId.asc().nullsLast().op("uuid_ops")),
	index("idx_deployment_status").using("btree", table.status.asc().nullsLast().op("enum_ops")),
	foreignKey({
			columns: [table.developerId],
			foreignColumns: [developers.id],
			name: "app_deployments_developer_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.providerResourceId],
			foreignColumns: [providerResources.id],
			name: "app_deployments_provider_resource_id_fkey"
		}),
]);

export const developerVms = pgTable("developer_vms", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	developerId: uuid("developer_id").notNull(),
	providerResourceId: uuid("provider_resource_id").notNull(),
	containerId: varchar("container_id", { length: 200 }).notNull(),
	ram: integer().notNull(),
	cpuCores: integer("cpu_cores").notNull(),
	storage: integer().notNull(),
	gpu: jsonb(),
	status: varchar({ length: 50 }).default('running').notNull(),
	sshPublicKey: text("ssh_public_key").notNull(),
	sshPort: integer("ssh_port").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	name: varchar({ length: 100 }).default('Unnamed Resource').notNull(),
}, (table) => [
	foreignKey({
			columns: [table.developerId],
			foreignColumns: [developers.id],
			name: "developer_vms_developer_id_fkey"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.providerResourceId],
			foreignColumns: [providerResources.id],
			name: "developer_vms_provider_resource_id_fkey"
		}).onDelete("cascade"),
	unique("developer_vms_container_id_key").on(table.containerId),
]);

export const verification = pgTable("verification", {
	id: text().primaryKey().notNull(),
	identifier: text().notNull(),
	value: text().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
});

export const session = pgTable("session", {
	id: text().primaryKey().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	token: text().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).notNull(),
	ipAddress: text("ip_address"),
	userAgent: text("user_agent"),
	userId: text("user_id").notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "session_user_id_user_id_fk"
		}).onDelete("cascade"),
	unique("session_token_unique").on(table.token),
]);

export const providerResources = pgTable("provider_resources", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	providerId: uuid("provider_id").notNull(),
	name: varchar({ length: 100 }).default('Unnamed Resource').notNull(),
	status: resourceStatus().default('pending').notNull(),
	ipAddress: varchar("ip_address", { length: 30 }),
	cpuCores: integer("cpu_cores").notNull(),
	ram: integer().notNull(),
	storage: integer().notNull(),
	bandwidth: integer(),
	gpu: jsonb(),
	sshPublicKey: text("ssh_public_key"),
	checkedSpecs: jsonb("checked_specs"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_resource_ip").using("btree", table.ipAddress.asc().nullsLast().op("text_ops")),
	index("idx_resource_name").using("btree", table.name.asc().nullsLast().op("text_ops")),
	index("idx_resource_provider").using("btree", table.providerId.asc().nullsLast().op("uuid_ops")),
	index("idx_resource_status").using("btree", table.status.asc().nullsLast().op("enum_ops")),
	foreignKey({
			columns: [table.providerId],
			foreignColumns: [providers.id],
			name: "provider_resources_provider_id_fkey"
		}).onDelete("cascade"),
]);

export const tunnels = pgTable("tunnels", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	providerContainerId: varchar("provider_container_id", { length: 200 }).notNull(),
	bastionId: uuid("bastion_id").notNull(),
	assignedPort: integer("assigned_port").notNull(),
	sshUsername: varchar("ssh_username", { length: 100 }),
	sshPublicKey: text("ssh_public_key"),
	status: varchar({ length: 50 }).default('assigned').notNull(),
	lastHealthCheck: timestamp("last_health_check", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	tunnelType: varchar("tunnel_type", { length: 50 }).default('agent').notNull(),
}, (table) => [
	index("idx_tunnel_bastion").using("btree", table.bastionId.asc().nullsLast().op("uuid_ops")),
	index("idx_tunnel_provider_container").using("btree", table.providerContainerId.asc().nullsLast().op("text_ops")),
	index("idx_tunnel_status").using("btree", table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.bastionId],
			foreignColumns: [bastions.id],
			name: "tunnels_bastion_id_fkey"
		}).onDelete("cascade"),
	unique("tunnels_bastion_id_assigned_port_key").on(table.bastionId, table.assignedPort),
]);

export const userSettings = pgTable("user_settings", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	notifications: jsonb().default({"push":false,"email":true}),
	privacy: jsonb().default({"profileVisible":true,"activityVisible":false}),
	security: jsonb().default({"twoFactorEnabled":false,"lastPasswordChange":null}),
	preferences: jsonb().default({"theme":"dark","language":"en","timezone":"UTC"}),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "user_settings_user_id_fk"
		}).onDelete("cascade"),
	unique("user_settings_user_id_unique").on(table.userId),
]);

export const healthChecks = pgTable("health_checks", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	providerId: uuid("provider_id").notNull(),
	results: jsonb().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.providerId],
			foreignColumns: [providers.id],
			name: "health_checks_provider_id_fkey"
		}).onDelete("cascade"),
]);

export const deploymentContainers = pgTable("deployment_containers", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	deploymentId: uuid("deployment_id").notNull(),
	containerId: varchar("container_id", { length: 200 }).notNull(),
	containerType: containerType("container_type").notNull(),
	name: varchar({ length: 100 }).notNull(),
	image: varchar({ length: 200 }).notNull(),
	cpuCores: integer("cpu_cores").notNull(),
	ram: integer().notNull(),
	storage: integer().notNull(),
	gpu: jsonb(),
	port: integer(),
	internalPort: integer("internal_port"),
	status: varchar({ length: 50 }).default('creating').notNull(),
	environmentVars: jsonb("environment_vars"),
	volumes: jsonb(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("idx_container_deployment").using("btree", table.deploymentId.asc().nullsLast().op("uuid_ops")),
	index("idx_container_status").using("btree", table.status.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.deploymentId],
			foreignColumns: [appDeployments.id],
			name: "deployment_containers_deployment_id_fkey"
		}).onDelete("cascade"),
	unique("deployment_containers_container_id_key").on(table.containerId),
]);

export const bastions = pgTable("bastions", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: varchar({ length: 100 }).notNull(),
	ipAddress: inet("ip_address").notNull(),
	portRangeStart: integer("port_range_start").notNull(),
	portRangeEnd: integer("port_range_end").notNull(),
	isActive: boolean("is_active").default(true),
	lastHeartbeat: timestamp("last_heartbeat", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	sshPublicKey: text("ssh_public_key"),
});
export const deploymentOverview = pgView("deployment_overview", {	deploymentId: uuid("deployment_id"),
	appType: appType("app_type"),
	deploymentStatus: deploymentStatus("deployment_status"),
	networkName: varchar("network_name", { length: 100 }),
	totalCpu: integer("total_cpu"),
	totalMemory: integer("total_memory"),
	containers: json(),
}).as(sql`SELECT d.id AS deployment_id, d.app_type, d.status AS deployment_status, d.network_name, d.total_cpu, d.total_memory, json_agg(json_build_object('container_id', c.container_id, 'container_type', c.container_type, 'status', c.status, 'port', c.port)) AS containers FROM app_deployments d LEFT JOIN deployment_containers c ON d.id = c.deployment_id GROUP BY d.id`);

export const providerResourcesOverview = pgView("provider_resources_overview", {	resourceId: uuid("resource_id"),
	resourceName: varchar("resource_name", { length: 100 }),
	resourceStatus: resourceStatus("resource_status"),
	ipAddress: varchar("ip_address", { length: 30 }),
	cpuCores: integer("cpu_cores"),
	ram: integer(),
	storage: integer(),
	gpu: jsonb(),
	sshPublicKey: text("ssh_public_key"),
	checkedSpecs: jsonb("checked_specs"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
}).as(sql`SELECT id AS resource_id, name AS resource_name, status AS resource_status, ip_address, cpu_cores, ram, storage, gpu, ssh_public_key, checked_specs, created_at, updated_at FROM provider_resources pr`);