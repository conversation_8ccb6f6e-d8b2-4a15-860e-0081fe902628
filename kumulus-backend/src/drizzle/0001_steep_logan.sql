CREATE TYPE "public"."resource_status" AS ENUM('ready', 'active', 'inactive', 'pending', 'installing', 'error');--> statement-breakpoint
CREATE TYPE "public"."tunnel_type" AS ENUM('agent', 'container');--> statement-breakpoint
CREATE TABLE "jwks" (
	"id" text PRIMARY KEY NOT NULL,
	"public_key" text NOT NULL,
	"private_key" text NOT NULL,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
ALTER TABLE "provider_resources" ALTER COLUMN "ip_address" SET DATA TYPE varchar(30);--> statement-breakpoint
ALTER TABLE "provider_resources" ALTER COLUMN "ip_address" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "provider_resources" ALTER COLUMN "bandwidth" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "provider_resources" ADD COLUMN "name" varchar(100) DEFAULT 'Unnamed Resource' NOT NULL;--> statement-breakpoint
ALTER TABLE "provider_resources" ADD COLUMN "status" "resource_status" DEFAULT 'pending' NOT NULL;--> statement-breakpoint
ALTER TABLE "bastions" ADD COLUMN "ssh_public_key" text;--> statement-breakpoint
ALTER TABLE "tunnels" ADD COLUMN "tunnel_type" varchar(50) DEFAULT 'agent' NOT NULL;--> statement-breakpoint
CREATE INDEX "idx_resource_ip" ON "provider_resources" USING btree ("ip_address" text_ops);--> statement-breakpoint
CREATE INDEX "idx_resource_name" ON "provider_resources" USING btree ("name" text_ops);--> statement-breakpoint
CREATE INDEX "idx_resource_provider" ON "provider_resources" USING btree ("provider_id" uuid_ops);--> statement-breakpoint
CREATE INDEX "idx_resource_status" ON "provider_resources" USING btree ("status" enum_ops);--> statement-breakpoint
CREATE VIEW "public"."provider_resources_overview" AS (SELECT id AS resource_id, name AS resource_name, status AS resource_status, ip_address, cpu_cores, ram, storage, gpu, ssh_public_key, checked_specs, created_at, updated_at FROM provider_resources pr);