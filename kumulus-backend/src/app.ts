import { load } from "https://deno.land/std@0.208.0/dotenv/mod.ts";
import { Hon<PERSON> } from "hono";
import type { AuthType } from "./utils/auth.ts";
import { cors } from "hono/cors";
import { auth, dashboard, profile, providers, developers, resources, vms, apps, bastions, tunnels, temp } from "./routes/index.ts";
// Disabled complex tunnel management routes
// import { tunnels } from "./routes/tunnels.ts";
// Disabled complex tunnel management systems
// import { healthMonitor } from "./services/health-monitor.ts";
// import { stateSynchronizer } from "./services/state-sync.ts";

// Load environment variables
await load({ export: true });

const app = new Hono<{ Variables: AuthType }>({
  strict: false,
});
// Configure CORS origins from environment variables
const corsOrigins = [
  ...(Deno.env.get("CORS_ORIGINS")?.split(","))
].filter(Boolean);

console.log("🔧 CORS Origins configured:", corsOrigins);

app.use(cors({
  origin: corsOrigins,
  allowHeaders: [
    "Content-Type",
    "Authorization",
    "Cookie",
    "X-Requested-With",
    "Accept",
    "Origin",
    "Cache-Control",
    "Pragma"
  ],
  allowMethods: ["POST", "GET", "PUT", "DELETE", "OPTIONS", "PATCH"],
  exposeHeaders: ["Content-Length", "Set-Cookie"],
  maxAge: 86400, // 24 hours
  credentials: true,
}));

// Mount routes
app.basePath("/api").route("/", auth);
app.basePath("/api").route("/dashboard", dashboard);
app.basePath("/api").route("/profile", profile);
app.basePath("/api").route("/providers", providers);
app.basePath("/api").route("/developers", developers);
app.basePath("/api").route("/resources", resources);
app.basePath("/api").route("/temp", temp);
app.basePath("/api").route("/vms", vms);
app.basePath("/api").route("/apps", apps);
app.basePath("/api").route("/bastions", bastions);
app.basePath("/api").route("/apps", apps);
// Disabled complex tunnel management routes
// app.basePath("/api").route("/tunnels", tunnels);

// Disabled complex tunnel management systems
// healthMonitor.start();
// stateSynchronizer.start();

//export default app;
export { app };
