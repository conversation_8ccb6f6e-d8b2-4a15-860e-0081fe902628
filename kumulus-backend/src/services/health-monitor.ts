import {
  cleanupTerminatedTunnels,
  getStaleTunnels,
  getTunnelHealthStats,
  updateTunnelHealth,
  updateTunnelStatus,
} from "../db/queries/tunnels-queries.ts";

export class HealthMonitor {
  private healthCheckInterval: number;
  private cleanupInterval: number;
  private healthCheckTimer?: number;
  private cleanupTimer?: number;
  private isRunning: boolean = false;
  private batchSize: number;
  private staleThresholdMinutes: number;

  constructor(
    healthCheckIntervalMs: number = 180000, // 3 minutes (was 30 seconds)
    cleanupIntervalMs: number = 3600000, // 1 hour (unchanged)
    batchSize: number = 100, // Process 100 tunnels at a time
    staleThresholdMinutes: number = 10, // Consider stale after 10 minutes (was 5)
  ) {
    this.healthCheckInterval = healthCheckIntervalMs;
    this.cleanupInterval = cleanupIntervalMs;
    this.batchSize = batchSize;
    this.staleThresholdMinutes = staleThresholdMinutes;
  }

  // Start health monitoring
  start(): void {
    if (this.isRunning) {
      console.log("Health monitor is already running");
      return;
    }

    this.isRunning = true;
    console.log("🔍 Starting tunnel health monitoring...");

    this.healthCheckTimer = setInterval(async () => {
      try {
        await this.performHealthChecks();
      } catch (error) {
        console.error("❌ Health check error:", error);
      }
    }, this.healthCheckInterval);

    this.cleanupTimer = setInterval(async () => {
      try {
        await this.performCleanup();
      } catch (error) {
        console.error("❌ Cleanup error:", error);
      }
    }, this.cleanupInterval);

    console.log(
      `✅ Health monitor started (checks every ${
        this.healthCheckInterval / 1000
      }s, cleanup every ${
        this.cleanupInterval / 1000 / 60
      }min, batch size: ${this.batchSize})`,
    );
  }

  // Stop health monitoring
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }

    console.log("🛑 Health monitor stopped");
  }

  // Perform health checks on stale tunnels (with batching)
  private async performHealthChecks(): Promise<void> {
    try {
      const staleTunnels = await getStaleTunnels(this.staleThresholdMinutes);

      if (staleTunnels.length === 0) {
        return;
      }

      console.log(
        `🔍 Checking health of ${staleTunnels.length} stale tunnels in batches of ${this.batchSize}...`,
      );

      // Process in batches to avoid overwhelming the system
      for (let i = 0; i < staleTunnels.length; i += this.batchSize) {
        const batch = staleTunnels.slice(i, i + this.batchSize);
        console.log(
          `🔄 Health check batch ${Math.floor(i / this.batchSize) + 1}/${
            Math.ceil(staleTunnels.length / this.batchSize)
          } (${batch.length} tunnels)`,
        );

        // Process batch with limited concurrency
        const promises = batch.map(async (tunnel) => {
          try {
            const isHealthy = await this.checkTunnelConnectivity(tunnel);

            if (!isHealthy) {
              console.log(
                `❌ Tunnel ${tunnel.id} appears disconnected, updating status`,
              );
              await updateTunnelStatus(
                tunnel.id,
                "disconnected",
                "Health check failed",
              );
            } else {
              await updateTunnelHealth(tunnel.id, true);
            }
          } catch (error) {
            console.error(
              `❌ Health check failed for tunnel ${tunnel.id}:`,
              error,
            );
            await updateTunnelStatus(
              tunnel.id,
              "disconnected",
              `Health check error: ${error.message}`,
            );
          }
        });

        // Wait for batch to complete with limited concurrency
        await Promise.allSettled(promises);

        // Small delay between batches
        if (i + this.batchSize < staleTunnels.length) {
          await new Promise((resolve) => setTimeout(resolve, 200)); // 200ms delay
        }
      }

      console.log(
        `✅ Completed health checks for ${staleTunnels.length} tunnels`,
      );
    } catch (error) {
      console.error("❌ Error during health checks:", error);
    }
  }

  // Check tunnel connectivity (simplified for now)
  private async checkTunnelConnectivity(tunnel: any): Promise<boolean> {
    try {
      // Use configurable threshold for stale detection
      // If tunnel hasn't been checked in more than threshold minutes, consider it potentially dead
      const lastCheck = tunnel.lastHealthCheck
        ? new Date(tunnel.lastHealthCheck)
        : null;
      const thresholdTime = new Date(
        Date.now() - this.staleThresholdMinutes * 60 * 1000,
      );

      if (!lastCheck || lastCheck < thresholdTime) {
        // In a real implementation, this would:
        // 1. Try to connect through the tunnel
        // 2. Send a ping/test packet
        // 3. Check with the bastion agent
        return false;
      }

      return true;
    } catch (error) {
      console.error(`Error checking tunnel ${tunnel.id} connectivity:`, error);
      return false;
    }
  }

  // Perform cleanup of old tunnels
  private async performCleanup(): Promise<void> {
    try {
      console.log("🧹 Starting tunnel cleanup...");

      // Cleanup terminated tunnels older than 24 hours
      const cleanupResult = await cleanupTerminatedTunnels(24);

      if (cleanupResult.rowsAffected && cleanupResult.rowsAffected > 0) {
        console.log(
          `🗑️ Cleaned up ${cleanupResult.rowsAffected} old terminated tunnels`,
        );
      }

      // Get current health stats for logging
      const stats = await getTunnelHealthStats();
      console.log("📊 Current tunnel stats:", stats);
    } catch (error) {
      console.error("❌ Error during cleanup:", error);
    }
  }

  // Get current health status
  async getHealthStatus(): Promise<any> {
    try {
      const stats = await getTunnelHealthStats();
      const staleCount = (await getStaleTunnels(5)).length;

      return {
        isRunning: this.isRunning,
        stats: {
          ...stats,
          stale: staleCount,
          total: Object.values(stats).reduce(
            (sum: number, count: number) => sum + count,
            0,
          ),
        },
        intervals: {
          healthCheckMs: this.healthCheckInterval,
          cleanupMs: this.cleanupInterval,
        },
        lastCheck: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error getting health status:", error);
      return {
        isRunning: this.isRunning,
        error: error.message,
      };
    }
  }

  // Manual health check trigger
  async triggerHealthCheck(): Promise<void> {
    console.log("🔍 Manual health check triggered");
    await this.performHealthChecks();
  }

  // Manual cleanup trigger
  async triggerCleanup(): Promise<void> {
    console.log("🧹 Manual cleanup triggered");
    await this.performCleanup();
  }
}

// Global health monitor instance with scale-optimized settings
export const healthMonitor = new HealthMonitor(
  180000, // 3 minutes between health checks (was 30 seconds)
  3600000, // 1 hour cleanup interval
  100, // Process 100 tunnels per batch
  10, // Consider tunnels stale after 10 minutes
);
