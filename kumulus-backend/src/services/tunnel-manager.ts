import {
  cleanupTerminatedTunnels,
  deleteTunnel,
  findAvailablePortForBastion,
  getStaleTunnels,
  getTunnelById,
  getTunnelHealthStats,
  insertTunnel,
  updateTunnelHealth,
  updateTunnelStatus,
} from "../db/queries/tunnels-queries.ts";
import {
  getAgentPort,
  getAnActiveBastion,
} from "../db/queries/bastions-queries.ts";

interface TunnelRequest {
  providerContainerId: string;
  sshUsername: string;
  sshPublicKey: string;
  tunnelType?: string;
  preferredBastionId?: string;
}

interface TunnelResult {
  success: boolean;
  tunnel?: any;
  bastionInfo?: any;
  error?: string;
  statusCode?: number;
}

interface HealthUpdate {
  isHealthy: boolean;
  responseTimeMs?: number;
  errorMessage?: string;
}

export class TunnelManager {
  constructor() {
    // Health monitoring is now handled by the separate HealthMonitor service
  }

  // Request tunnel creation
  async requestTunnel(request: TunnelRequest): Promise<TunnelResult> {
    try {
      // 1. Select bastion (preferred or active)
      let bastion;
      if (request.preferredBastionId) {
        // TODO: Add query to get specific bastion by ID
        bastion = await getAnActiveBastion();
      } else {
        bastion = await getAnActiveBastion();
      }

      if (!bastion || bastion.length === 0) {
        return {
          success: false,
          error: "No active bastion available",
          statusCode: 503,
        };
      }

      const bastionInfo = bastion[0];

      // 2. Find available port based on tunnel type
      let availablePort: number;
      const tunnelType = request.tunnelType || "agent";

      try {
        if (tunnelType === "agent") {
          // Agent tunnels use sequential allocation in 7777-7877 range
          availablePort = await getAgentPort(bastionInfo.id);
          console.log(
            `🔌 Allocated agent port ${availablePort} for bastion ${bastionInfo.id}`,
          );
        } else {
          // Container/VM/App tunnels use configurable range (default 30000-30100)
          const portResult = await findAvailablePortForBastion(
            bastionInfo.id,
            bastionInfo.portRangeStart || 30000,
            bastionInfo.portRangeEnd || 30100,
          );

          if (!portResult) {
            return {
              success: false,
              error: `No available ${tunnelType} ports on bastion (range: ${
                bastionInfo.portRangeStart || 30000
              }-${bastionInfo.portRangeEnd || 30100})`,
              statusCode: 503,
            };
          }

          availablePort = portResult;
          console.log(
            `🔌 Allocated ${tunnelType} port ${availablePort} for bastion ${bastionInfo.id}`,
          );
        }
      } catch (error) {
        return {
          success: false,
          error: error.message || "Port allocation failed",
          statusCode: 503,
        };
      }

      // 3. Create tunnel record in database
      const tunnelResult = await insertTunnel(
        request.providerContainerId,
        bastionInfo.id,
        availablePort,
        request.sshUsername,
        request.sshPublicKey,
        tunnelType,
        "assigned",
      );

      // 4. For now, we just create the database record
      // The actual tunnel will be established by the flare agent
      // when it connects to the bastion using the provided configuration

      return {
        success: true,
        tunnel: {
          id: tunnelResult.insertId,
          bastionId: bastionInfo.id,
          assignedPort: availablePort,
          status: "assigned",
        },
        bastionInfo: {
          ip: bastionInfo.ip,
          port: availablePort,
          sshPublicKey: bastionInfo.sshPublicKey,
        },
      };
    } catch (error) {
      console.error("Error requesting tunnel:", error);
      return {
        success: false,
        error: "Internal server error",
        statusCode: 500,
      };
    }
  }

  // Terminate tunnel
  async terminateTunnel(tunnelId: string): Promise<TunnelResult> {
    try {
      // 1. Get tunnel info
      const tunnel = await getTunnelById(tunnelId);
      if (!tunnel || tunnel.length === 0) {
        return {
          success: false,
          error: "Tunnel not found",
          statusCode: 404,
        };
      }

      const tunnelInfo = tunnel[0];

      // 2. Update tunnel status to terminated
      await updateTunnelStatus(tunnelId, "terminated");

      return { success: true };
    } catch (error) {
      console.error("Error terminating tunnel:", error);
      return {
        success: false,
        error: "Internal server error",
        statusCode: 500,
      };
    }
  }

  // Update tunnel health
  async updateTunnelHealth(
    tunnelId: string,
    healthUpdate: HealthUpdate,
  ): Promise<void> {
    await updateTunnelHealth(
      tunnelId,
      healthUpdate.isHealthy,
      healthUpdate.responseTimeMs,
    );
  }

  // Get health statistics
  async getHealthStats(): Promise<any> {
    const stats = await getTunnelHealthStats();
    const staleCount = (await getStaleTunnels()).length;

    return {
      ...stats,
      stale: staleCount,
      total: Object.values(stats).reduce((sum, count) => sum + count, 0),
    };
  }
}
