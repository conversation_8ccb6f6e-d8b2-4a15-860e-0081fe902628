import {
  getTunnelsByBastionId,
  getTunnelsByStatus,
  updateTunnelStatus,
} from "../db/queries/tunnels-queries.ts";
import { getAllBastions } from "../db/queries/bastions-queries.ts";

export class StateSynchronizer {
  private syncInterval: number;
  private syncTimer?: number;
  private isRunning: boolean = false;
  private batchSize: number;
  private maxConcurrentChecks: number;

  constructor(
    syncIntervalMs: number = 300000, // 5 minutes (was 1 minute)
    batchSize: number = 50, // Process 50 tunnels at a time
    maxConcurrentChecks: number = 10, // Max 10 concurrent operations
  ) {
    this.syncInterval = syncIntervalMs;
    this.batchSize = batchSize;
    this.maxConcurrentChecks = maxConcurrentChecks;
  }

  // Start state synchronization
  start(): void {
    if (this.isRunning) {
      console.log("State synchronizer is already running");
      return;
    }

    this.isRunning = true;
    console.log("🔄 Starting state synchronization...");

    this.syncTimer = setInterval(async () => {
      try {
        await this.performSync();
      } catch (error) {
        console.error("❌ State sync error:", error);
      }
    }, this.syncInterval);

    console.log(
      `✅ State synchronizer started (sync every ${
        this.syncInterval / 1000
      }s, batch size: ${this.batchSize})`,
    );
  }

  // Stop state synchronization
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = undefined;
    }

    console.log("🛑 State synchronizer stopped");
  }

  // Perform state synchronization
  private async performSync(): Promise<void> {
    try {
      console.log("🔄 Performing state synchronization...");

      // For now, we'll focus on basic state reconciliation
      // In a full implementation, this would:
      // 1. Query bastion agents for their actual tunnel state
      // 2. Compare with database state
      // 3. Reconcile differences

      await this.reconcileTunnelStates();
    } catch (error) {
      console.error("❌ Error during state sync:", error);
    }
  }

  // Reconcile tunnel states between database and reality (with batching)
  private async reconcileTunnelStates(): Promise<void> {
    try {
      // Get all "connected" tunnels from database
      const connectedTunnels = await getTunnelsByStatus("connected");

      if (connectedTunnels.length === 0) {
        return;
      }

      console.log(
        `🔍 Checking ${connectedTunnels.length} connected tunnels in batches of ${this.batchSize}...`,
      );

      // Process tunnels in batches to avoid overwhelming the system
      for (let i = 0; i < connectedTunnels.length; i += this.batchSize) {
        const batch = connectedTunnels.slice(i, i + this.batchSize);
        console.log(
          `🔄 Processing batch ${Math.floor(i / this.batchSize) + 1}/${
            Math.ceil(connectedTunnels.length / this.batchSize)
          } (${batch.length} tunnels)`,
        );

        // Process batch with limited concurrency
        await this.processTunnelBatch(batch);

        // Small delay between batches to prevent overwhelming the database
        if (i + this.batchSize < connectedTunnels.length) {
          await new Promise((resolve) => setTimeout(resolve, 100)); // 100ms delay
        }
      }

      console.log(
        `✅ Completed tunnel state reconciliation for ${connectedTunnels.length} tunnels`,
      );
    } catch (error) {
      console.error("❌ Error reconciling tunnel states:", error);
    }
  }

  // Process a batch of tunnels with limited concurrency
  private async processTunnelBatch(tunnels: any[]): Promise<void> {
    const semaphore = new Array(this.maxConcurrentChecks).fill(null);
    let semaphoreIndex = 0;

    const promises = tunnels.map(async (tunnel) => {
      // Wait for available slot in semaphore
      const slotIndex = semaphoreIndex % this.maxConcurrentChecks;
      semaphoreIndex++;

      try {
        const isActuallyConnected = await this.verifyTunnelConnection(tunnel);

        if (!isActuallyConnected) {
          console.log(
            `❌ Tunnel ${tunnel.id} appears disconnected, updating status`,
          );
          await updateTunnelStatus(
            tunnel.id,
            "disconnected",
            "State sync detected disconnection",
          );
        }
      } catch (error) {
        console.error(`❌ Error verifying tunnel ${tunnel.id}:`, error);
      }
    });

    // Wait for all promises in batch to complete
    await Promise.allSettled(promises);
  }

  // Verify if a tunnel is actually connected
  private async verifyTunnelConnection(tunnel: any): Promise<boolean> {
    try {
      // For now, use a simple heuristic:
      // If tunnel hasn't been health checked in the last 15 minutes, consider it disconnected
      const lastCheck = tunnel.lastHealthCheck
        ? new Date(tunnel.lastHealthCheck)
        : null;
      const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

      if (!lastCheck || lastCheck < fifteenMinutesAgo) {
        return false;
      }

      // In a full implementation, this would:
      // 1. Query the bastion agent to check if tunnel is running
      // 2. Test the actual tunnel connection
      // 3. Verify port is still allocated

      return true;
    } catch (error) {
      console.error(`Error verifying tunnel ${tunnel.id}:`, error);
      return false;
    }
  }

  // Manual sync trigger
  async triggerSync(): Promise<void> {
    console.log("🔄 Manual state sync triggered");
    await this.performSync();
  }

  // Get sync status
  getSyncStatus(): any {
    return {
      isRunning: this.isRunning,
      syncIntervalMs: this.syncInterval,
      lastSync: new Date().toISOString(),
    };
  }

  // Reconcile specific bastion state
  async reconcileBastionState(bastionId: string): Promise<void> {
    try {
      console.log(`🔄 Reconciling state for bastion ${bastionId}...`);

      // Get all tunnels for this bastion from database
      const bastionTunnels = await getTunnelsByBastionId(bastionId);

      // In a full implementation, this would:
      // 1. Query the bastion agent for its actual running tunnels
      // 2. Compare with database state
      // 3. Update database to match reality
      // 4. Or instruct bastion to create missing tunnels

      console.log(
        `📊 Found ${bastionTunnels.length} tunnels for bastion ${bastionId}`,
      );
    } catch (error) {
      console.error(`❌ Error reconciling bastion ${bastionId} state:`, error);
    }
  }

  // Handle provider IP address changes
  async handleProviderIpChange(
    providerContainerId: string,
    oldIp: string,
    newIp: string,
  ): Promise<void> {
    try {
      console.log(
        `🔄 Handling IP change for provider ${providerContainerId}: ${oldIp} -> ${newIp}`,
      );

      // In a full implementation, this would:
      // 1. Find all tunnels for this provider
      // 2. Update tunnel configurations with new IP
      // 3. Restart tunnels if necessary
      // 4. Update any cached configurations

      console.log(`✅ IP change handled for provider ${providerContainerId}`);
    } catch (error) {
      console.error(
        `❌ Error handling IP change for provider ${providerContainerId}:`,
        error,
      );
    }
  }

  // Handle bastion address changes
  async handleBastionAddressChange(
    bastionId: string,
    oldAddress: string,
    newAddress: string,
  ): Promise<void> {
    try {
      console.log(
        `🔄 Handling bastion address change ${bastionId}: ${oldAddress} -> ${newAddress}`,
      );

      // Get all tunnels using this bastion
      const affectedTunnels = await getTunnelsByBastionId(bastionId);

      console.log(
        `📊 Found ${affectedTunnels.length} tunnels affected by bastion address change`,
      );

      // In a full implementation, this would:
      // 1. Update all affected tunnel configurations
      // 2. Notify providers of the new bastion address
      // 3. Restart tunnels with new configuration
      // 4. Update any cached configurations

      for (const tunnel of affectedTunnels) {
        // Mark tunnels as needing reconfiguration
        await updateTunnelStatus(
          tunnel.id,
          "disconnected",
          "Bastion address changed",
        );
      }

      console.log(`✅ Bastion address change handled for ${bastionId}`);
    } catch (error) {
      console.error(
        `❌ Error handling bastion address change for ${bastionId}:`,
        error,
      );
    }
  }
}

// Global state synchronizer instance with scale-optimized settings
export const stateSynchronizer = new StateSynchronizer(
  300000, // 5 minutes between sync cycles (was 1 minute)
  50, // Process 50 tunnels per batch
  10, // Max 10 concurrent operations
);
