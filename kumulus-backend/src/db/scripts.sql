-- BetterAuth Tables

-- Empty all tables
--TRUNCATE TABLE "user", "account", "verification", "session", "providers", "developers", "app_deployments", "provider_resources", "developer_vms", "tunnels", "bastions" CASCADE;


----------------------------
-- TYPES
----------------------------
CREATE TYPE app_type AS ENUM (
    'odoo',
    'wordpress',
    'libreoffice',
    'nextcloud'
);

CREATE TYPE container_type AS ENUM (
    'main',
    'database',
    'cache',
    'proxy'
);

CREATE TYPE deployment_status AS ENUM (
    'creating',
    'running',
    'stopped',
    'failed',
    'deleted'
);

CREATE TYPE resource_status AS ENUM (
    'ready',
    'active',
    'inactive',
    'pending',
    'installing',
    'error' 
);

CREATE TYPE tunnel_type AS ENUM (
    'agent',
    'container'
);

----------------------------
-- DEPLOYMENT TABLES
----------------------------

CREATE TABLE providers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT UNIQUE NOT NULL, -- Links to the 'id' of the user in the BetterAuth 'user' table.
    name VARCHAR(100) NOT NULL,
    wallet_address VARCHAR(200) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    score INTEGER DEFAULT 50,
    last_lease_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE provider_resources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider_id UUID NOT NULL REFERENCES providers(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL DEFAULT 'Unnamed Resource',
    status resource_status NOT NULL DEFAULT 'pending',
    ip_address VARCHAR(30),
    cpu_cores INTEGER NOT NULL,
    ram INTEGER NOT NULL, -- in MB
    storage INTEGER NOT NULL, -- in GB
    bandwidth INTEGER,
    gpu JSONB,
    ssh_public_key TEXT,
    checked_specs JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE health_checks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider_id UUID NOT NULL REFERENCES providers(id) ON DELETE CASCADE,
    results JSONB NOT NULL
);

CREATE TABLE developers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT UNIQUE NOT NULL, -- Links to the 'id' of the user in the BetterAuth 'user' table.
    name VARCHAR(100) NOT NULL,
    wallet_address VARCHAR(200) UNIQUE NOT NULL,
    ssh_public_key TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE developer_vms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    developer_id UUID NOT NULL REFERENCES developers(id) ON DELETE CASCADE,
    provider_resource_id UUID NOT NULL REFERENCES provider_resources(id) ON DELETE CASCADE,
    container_id VARCHAR(200) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL DEFAULT 'Awesome VM',
    ram INTEGER NOT NULL,
    cpu_cores INTEGER NOT NULL,
    storage INTEGER NOT NULL,
    gpu JSONB,
    status VARCHAR(50) NOT NULL DEFAULT 'running',
    ssh_public_key TEXT NOT NULL,
    ssh_port INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE app_deployments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    developer_id UUID NOT NULL REFERENCES developers(id) ON DELETE CASCADE,
    provider_resource_id UUID NOT NULL REFERENCES provider_resources(id),
    app_type app_type NOT NULL,
    network_name VARCHAR(100) NOT NULL,
    status deployment_status NOT NULL DEFAULT 'creating',
    total_cpu INTEGER NOT NULL,
    total_memory INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE TABLE deployment_containers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    deployment_id UUID NOT NULL REFERENCES app_deployments(id) ON DELETE CASCADE,
    container_id VARCHAR(200) NOT NULL UNIQUE,
    container_type container_type NOT NULL,
    name VARCHAR(100) NOT NULL,
    image VARCHAR(200) NOT NULL,
    cpu_cores INTEGER NOT NULL,
    ram INTEGER NOT NULL,
    storage INTEGER NOT NULL,
    gpu JSONB,
    port INTEGER,
    internal_port INTEGER,
    status VARCHAR(50) NOT NULL DEFAULT 'creating',
    environment_vars JSONB,
    volumes JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

----------------------------
-- BASTION & TUNNELING TABLES
----------------------------
CREATE TABLE bastions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    ip_address INET NOT NULL,
    port_range_start INTEGER NOT NULL,
    port_range_end INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_heartbeat TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- There are 2 types of tunnels: agent tunnels and app tunnels.
CREATE TABLE tunnels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider_container_id VARCHAR(200) NOT NULL,
    bastion_id UUID NOT NULL REFERENCES bastions(id) ON DELETE CASCADE,
    assigned_port INTEGER NOT NULL,
    ssh_username VARCHAR(100),
    ssh_public_key TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'assigned',
    last_health_check TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    type tunnel_type NOT NULL DEFAULT 'agent',
    UNIQUE(bastion_id, assigned_port)
);

-- Create Bastions table


----------------------------
-- INDEXES
----------------------------
CREATE INDEX idx_deployment_status ON app_deployments(status);
CREATE INDEX idx_deployment_developer ON app_deployments(developer_id);
CREATE INDEX idx_deployment_provider ON app_deployments(provider_resource_id);
CREATE INDEX idx_container_deployment ON deployment_containers(deployment_id);
CREATE INDEX idx_container_status ON deployment_containers(status);
CREATE INDEX idx_resource_status ON provider_resources(status);
CREATE INDEX idx_resource_provider ON provider_resources(provider_id);
CREATE INDEX idx_resource_name ON provider_resources(name);
CREATE INDEX idx_resource_ip ON provider_resources(ip_address);
CREATE INDEX idx_tunnel_status ON tunnels(status);
CREATE INDEX idx_tunnel_bastion ON tunnels(bastion_id);
CREATE INDEX idx_tunnel_provider_container ON tunnels(provider_container_id);


----------------------------
-- VIEWS
----------------------------
CREATE VIEW deployment_overview AS
SELECT
    d.id as deployment_id,
    d.app_type,
    d.status as deployment_status,
    d.network_name,
    d.total_cpu,
    d.total_memory,
    json_agg(json_build_object(
        'container_id', c.container_id,
        'container_type', c.container_type,
        'status', c.status,
        'port', c.port
    )) as containers
FROM app_deployments d
LEFT JOIN deployment_containers c ON d.id = c.deployment_id
GROUP BY d.id;


-- Create view for provider_resources
 CREATE VIEW provider_resources_overview AS
 SELECT
     pr.id as resource_id,
     pr.name as resource_name,
     pr.status as resource_status,
     pr.ip_address,
     pr.cpu_cores,
     pr.ram,
     pr.storage,
     pr.gpu,
     pr.ssh_public_key,
     pr.checked_specs,
     pr.created_at,
     pr.updated_at
 FROM provider_resources pr;    




-- Migration: Add name field to developer_vms table
-- This migration adds a name field to store user-friendly VM names

-- Add the name column (nullable initially to handle existing records)
--ALTER TABLE developer_vms ADD COLUMN IF NOT EXISTS name VARCHAR(100);

-- Update existing records to use container_id as name (fallback)
--UPDATE developer_vms 
--SET name = container_id 
--WHERE name IS NULL OR name = '';

-- Make the name column NOT NULL after populating existing records
--ALTER TABLE developer_vms ALTER COLUMN name SET NOT NULL;

-- Add a comment to document the field
--COMMENT ON COLUMN developer_vms.name IS 'User-friendly name for the VM';

----------------

--Update provider_resources Set status = 'ready' where id = '43cf8e92-dfca-48b0-b3a7-90deab940a71';

-- Insert Bastion
-- INSERT INTO bastions (name, ip_address, port_range_start, port_range_end) VALUES ('Bastion Agent1', '*************', 18000, 19000);

-- Update tunnels table and add tunnel_type column
-- ALTER TABLE tunnels ADD COLUMN tunnel_type VARCHAR(50) NOT NULL DEFAULT 'agent';

-- Update bastion with id 3d5df34b-cb4c-4747-9ce8-f3dd38589572 insert a public key value
-- UPDATE bastions SET ssh_public_key = 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDBkeDKEvA86an2D+7XKTr0QJEzl+Mmr0F1Z6jfAgYynObLxDGRqaZgAiHES8FTlaTdDAFcMHtqpxduMMGKMy2ic8dryLYxUeowkafkV8fO2qsk9YjZZI3i2FVaPKHOgaY2hDGBon5tti2FSoCKNhoO3yCdr5M6aDGLu/5TfH+ZKDNr1M13mWI8s8UvAQbZ1gEeclKF6R7VfOsW3EwpYyN17pHQKrZCx2v+yvCSp0qnAIjWmo9zvC7PcU4Vp4RqcuyMheT/qBr6PfhwewT364syymce/heFBa3yfJBPuGSOkGd4aHRaZhcklBqCwpcOkdI/DF1RuUdKZ7/QgeBKy757uPacuF/Ih6ysWIcy9z20l7EKp7n3JhABDSHwhwkboEQMnCVyUf0qDn/BWrQHSC+t8mrBNdZmcEg6VGwqhPWhKmqFXwyIbPJPFAwSfg1u8V7crIBPUd92RmzBEokV/zs42KnIKr896WK0aPXbbziMB/DCsLXOV0Oqno6kZZezbyp5u11S+0IWs7U3/quPZzFvtmGAaLqi5V7Edq3bcvbxcLTfcYrkwRMY+fF09IL/OT6tjVjtdNWUyL82lV3nOJih2xR3OHAZBfBfopcNTUO4w3m9KhBG89dk0dw+6BQaAvTF2bLckUWFI6HBGOCD+Eik9LrOFrPMWhZFrYAydC9FJQ== ubuntu@ip-172-31-26-204' where id = '3d5df34b-cb4c-4747-9ce8-f3dd38589572';

-- Update bastion ip address
-- UPDATE bastions SET ip_address = '*************' where id = '3d5df34b-cb4c-4747-9ce8-f3dd38589572';

--TRUNCATE TABLE "user", "account", "verification", "session", "providers", "developers", "app_deployments", "provider_resources", "developer_vms", "tunnels", "bastions", "user_settings" CASCADE;
-- Update users set verified to true
