import { drizzle } from "drizzle-orm/node-postgres";
import pg from "pg";
import * as schema from "../drizzle/schema.ts";
const { Pool } = pg;

class Database {
  private static instance: Database;
  private db: ReturnType<typeof drizzle>;

  private constructor() {
    const databaseUrl = Deno.env.get("DATABASE_URL");

    const pool = new Pool({
      connectionString: databaseUrl,
    });

    this.db = drizzle(pool, {
      schema: schema,
    });
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  public getConnection() {
    return this.db;
  }
}

export const db = Database.getInstance().getConnection();
