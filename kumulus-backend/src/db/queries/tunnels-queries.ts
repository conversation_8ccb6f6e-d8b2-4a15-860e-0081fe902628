import {
  bastions as bastionSchema,
  tunnels as tunnelSchema,
} from "../../drizzle/schema.ts";
import { db } from "../db.ts";
import { and, eq, isNull, lt, or, sql } from "drizzle-orm";

// Insert Tunnel with enhanced parameters
export async function insertTunnel(
  providerContainerId: string,
  bastionId: string,
  assignedPort: number,
  sshUsername: string,
  sshPublicKey: string,
  tunnelType: string = "agent",
  status: string = "assigned",
) {
  return db.insert(tunnelSchema).values({
    providerContainerId,
    bastionId,
    assignedPort,
    sshUsername,
    sshPublicKey,
    tunnelType,
    status,
  });
}

// Get Tunnel by ID with bastion info
export async function getTunnelById(tunnelId: string) {
  return db.select({
    id: tunnelSchema.id,
    providerContainerId: tunnelSchema.providerContainerId,
    bastionId: tunnelSchema.bastionId,
    assignedPort: tunnelSchema.assignedPort,
    sshUsername: tunnelSchema.sshUsername,
    sshPublicKey: tunnelSchema.sshPublicKey,
    status: tunnelSchema.status,
    lastHealthCheck: tunnelSchema.lastHealthCheck,
    tunnelType: tunnelSchema.tunnelType,
    createdAt: tunnelSchema.createdAt,
    updatedAt: tunnelSchema.updatedAt,
    bastionIp: bastionSchema.ipAddress,
    bastionName: bastionSchema.name,
  })
    .from(tunnelSchema)
    .leftJoin(bastionSchema, eq(tunnelSchema.bastionId, bastionSchema.id))
    .where(eq(tunnelSchema.id, tunnelId));
}

// Get Tunnels by Bastion ID
export async function getTunnelsByBastionId(bastionId: string) {
  return db.select().from(tunnelSchema).where(
    eq(tunnelSchema.bastionId, bastionId),
  );
}

// Get Tunnels by Status
export async function getTunnelsByStatus(status: string) {
  return db.select({
    id: tunnelSchema.id,
    providerContainerId: tunnelSchema.providerContainerId,
    bastionId: tunnelSchema.bastionId,
    assignedPort: tunnelSchema.assignedPort,
    status: tunnelSchema.status,
    lastHealthCheck: tunnelSchema.lastHealthCheck,
    tunnelType: tunnelSchema.tunnelType,
    createdAt: tunnelSchema.createdAt,
    bastionIp: bastionSchema.ipAddress,
    bastionName: bastionSchema.name,
  })
    .from(tunnelSchema)
    .leftJoin(bastionSchema, eq(tunnelSchema.bastionId, bastionSchema.id))
    .where(eq(tunnelSchema.status, status));
}

// Update Tunnel Status
export async function updateTunnelStatus(
  tunnelId: string,
  status: string,
  errorMessage?: string,
) {
  const updates: any = {
    status,
    updatedAt: new Date().toISOString(),
  };

  if (status === "connected") {
    updates.lastHealthCheck = new Date().toISOString();
  }

  return db.update(tunnelSchema).set(updates).where(
    eq(tunnelSchema.id, tunnelId),
  );
}

// Update Tunnel Health
export async function updateTunnelHealth(
  tunnelId: string,
  isHealthy: boolean,
  responseTimeMs?: number,
) {
  const updates: any = {
    lastHealthCheck: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // Update status based on health
  if (!isHealthy) {
    updates.status = "disconnected";
  } else if (isHealthy) {
    updates.status = "connected";
  }

  return db.update(tunnelSchema).set(updates).where(
    eq(tunnelSchema.id, tunnelId),
  );
}

// Update Tunnel
export async function updateTunnel(
  tunnelId: string,
  updates: any,
) {
  return db.update(tunnelSchema).set({
    ...updates,
    updatedAt: new Date().toISOString(),
  }).where(
    eq(tunnelSchema.id, tunnelId),
  );
}

// Delete Tunnel
export async function deleteTunnel(tunnelId: string) {
  return db.delete(tunnelSchema).where(eq(tunnelSchema.id, tunnelId));
}

// Get Stale Tunnels (no health check in last X minutes)
export async function getStaleTunnels(minutesThreshold: number = 5) {
  const thresholdTime = new Date(Date.now() - minutesThreshold * 60 * 1000)
    .toISOString();

  return db.select({
    id: tunnelSchema.id,
    providerContainerId: tunnelSchema.providerContainerId,
    bastionId: tunnelSchema.bastionId,
    assignedPort: tunnelSchema.assignedPort,
    status: tunnelSchema.status,
    lastHealthCheck: tunnelSchema.lastHealthCheck,
    bastionIp: bastionSchema.ipAddress,
  })
    .from(tunnelSchema)
    .leftJoin(bastionSchema, eq(tunnelSchema.bastionId, bastionSchema.id))
    .where(
      and(
        or(
          lt(tunnelSchema.lastHealthCheck, thresholdTime),
          isNull(tunnelSchema.lastHealthCheck),
        ),
        eq(tunnelSchema.status, "connected"),
      ),
    );
}

// Get Tunnel Health Stats
export async function getTunnelHealthStats() {
  const stats = await db.select({
    status: tunnelSchema.status,
    count: sql<number>`count(*)`,
  })
    .from(tunnelSchema)
    .groupBy(tunnelSchema.status);

  return stats.reduce((acc, stat) => {
    acc[stat.status] = stat.count;
    return acc;
  }, {} as Record<string, number>);
}

// Find Available Port for Bastion
export async function findAvailablePortForBastion(
  bastionId: string,
  portRangeStart: number,
  portRangeEnd: number,
) {
  const usedPorts = await db.select({
    port: tunnelSchema.assignedPort,
  })
    .from(tunnelSchema)
    .where(eq(tunnelSchema.bastionId, bastionId));

  const usedPortSet = new Set(usedPorts.map((p) => p.port));

  for (let port = portRangeStart; port <= portRangeEnd; port++) {
    if (!usedPortSet.has(port)) {
      return port;
    }
  }

  return null;
}

// Get Tunnels by Provider Container ID
export async function getTunnelsByProviderContainerId(
  providerContainerId: string,
) {
  return db.select({
    id: tunnelSchema.id,
    bastionId: tunnelSchema.bastionId,
    assignedPort: tunnelSchema.assignedPort,
    status: tunnelSchema.status,
    tunnelType: tunnelSchema.tunnelType,
    lastHealthCheck: tunnelSchema.lastHealthCheck,
    createdAt: tunnelSchema.createdAt,
    bastionIp: bastionSchema.ipAddress,
    bastionName: bastionSchema.name,
  })
    .from(tunnelSchema)
    .leftJoin(bastionSchema, eq(tunnelSchema.bastionId, bastionSchema.id))
    .where(eq(tunnelSchema.providerContainerId, providerContainerId));
}

// Cleanup Terminated Tunnels (older than X hours)
export async function cleanupTerminatedTunnels(hoursThreshold: number = 24) {
  const thresholdTime = new Date(Date.now() - hoursThreshold * 60 * 60 * 1000)
    .toISOString();

  return db.delete(tunnelSchema).where(
    and(
      eq(tunnelSchema.status, "terminated"),
      lt(tunnelSchema.updatedAt, thresholdTime),
    ),
  );
}
