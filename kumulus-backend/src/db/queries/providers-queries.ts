import {
  providerResources as resourceSchema,
  providers as providerSchema,
} from "../../drizzle/schema.ts";
import { db } from "../db.ts";
import { and, eq, gte } from "drizzle-orm";

// Insert Provider
export function insertProvider(
  userId: string,
  name: string,
  walletAddress: string,
) {
  return db.insert(providerSchema).values({
    userId,
    name,
    walletAddress,
  });
}

// Get Provider by ID
export function getProviderById(providerId: string) {
  return db.select().from(providerSchema).where(
    eq(providerSchema.id, providerId),
  );
}

// Update Provider
export function updateProvider(
  providerId: string,
  updates: Partial<typeof providerSchema>,
) {
  return db.update(providerSchema).set(updates).where(
    eq(providerSchema.id, providerId),
  );
}

// Delete Provider
export function deleteProvider(providerId: string) {
  return db.delete(providerSchema).where(eq(providerSchema.id, providerId));
}

// get providerId by user id
export function getProviderIdByUserId(userId: string) {
  // return only the provider id
  return db.select({ id: providerSchema.id }).from(providerSchema).where(
    eq(providerSchema.userId, userId),
  ).limit(1);
}

// Get current developer ID (mock function for now)
export async function getCurrentDeveloperId() {
  // This should be replaced with actual developer ID logic
  // For now, return a mock developer ID
  return "mock-developer-id";
}

// Select provider with available resources
export async function selectProvider(
  cpu: number,
  ram: number,
  storage: number,
) {
  return db.select({
    id: resourceSchema.id,
    providerId: resourceSchema.providerId,
    cpuCores: resourceSchema.cpuCores,
    ram: resourceSchema.ram,
    storage: resourceSchema.storage,
    ipAddress: resourceSchema.ipAddress,
  })
    .from(resourceSchema)
    .where(
      and(
        gte(resourceSchema.cpuCores, cpu),
        gte(resourceSchema.ram, ram),
        gte(resourceSchema.storage, storage),
        eq(resourceSchema.status, "active"), // Resources that are active and ready to accept VMs
      ),
    )
    .orderBy(resourceSchema.cpuCores)
    .limit(1);
}
