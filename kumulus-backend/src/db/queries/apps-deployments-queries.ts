import {
  appDeployments as appDeploymentsSchema,
  deploymentContainers as deploymentContainersSchema,
} from "../../drizzle/schema.ts";
import { db } from "../db.ts";
import { eq } from "drizzle-orm";

// Insert App Deployment
export async function insertAppDeployment(deploymentData: {
  id: string;
  developerId: string;
  providerResourceId: string;
  appType: string;
  networkName: string;
  totalCpu: number;
  totalMemory: number;
  status?: string;
}) {
  return db.insert(appDeploymentsSchema).values(deploymentData);
}

// Insert App Deployment (legacy function signature)
export async function insertAppDeploymentLegacy(
  developerId: string,
  providerResourceId: string,
  appType: string,
  networkName: string,
  totalCpu: number,
  totalMemory: number,
) {
  return db.insert(appDeploymentsSchema).values({
    developerId,
    providerResourceId,
    appType,
    networkName,
    totalCpu,
    totalMemory,
  });
}

// Get App Deployment by ID
export async function getAppDeploymentById(deploymentId: string) {
  return db.select().from(appDeploymentsSchema).where(
    eq(appDeploymentsSchema.id, deploymentId),
  );
}

// Update App Deployment
export async function updateAppDeployment(
  deploymentId: string,
  updates: Partial<typeof appDeploymentsSchema>,
) {
  return db.update(appDeploymentsSchema).set(updates).where(
    eq(appDeploymentsSchema.id, deploymentId),
  );
}

// Delete App Deployment
export async function deleteAppDeployment(deploymentId: string) {
  return db.delete(appDeploymentsSchema).where(
    eq(appDeploymentsSchema.id, deploymentId),
  );
}

// Insert Deployment Container
export async function insertDeploymentContainers(containerData: {
  deploymentId: string;
  containerId: string;
  containerType: string;
  name: string;
  image: string;
  cpuCores: number;
  ram: number;
  storage: number;
  gpu?: any;
  port?: number;
  internalPort?: number;
  status?: string;
  environmentVars?: any;
  volumes?: any;
}) {
  return db.insert(deploymentContainersSchema).values(containerData);
}

// Update Deployment Container Status
export async function updateDeploymentContainersStatus(
  containerId: string,
  status: string,
) {
  return db.update(deploymentContainersSchema)
    .set({ status })
    .where(eq(deploymentContainersSchema.containerId, containerId));
}

// Get Deployment Containers by Deployment ID
export async function getDeploymentContainersByDeploymentId(
  deploymentId: string,
) {
  return db.select().from(deploymentContainersSchema)
    .where(eq(deploymentContainersSchema.deploymentId, deploymentId));
}

// Get Deployment Container by Container ID
export async function getDeploymentContainerByContainerId(containerId: string) {
  return db.select().from(deploymentContainersSchema)
    .where(eq(deploymentContainersSchema.containerId, containerId))
    .limit(1);
}
