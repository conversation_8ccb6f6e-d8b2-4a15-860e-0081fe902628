import { db } from "../db.ts";
import { developerVms as developersVms, providerResources } from "../../drizzle/schema.ts";
import { eq } from "drizzle-orm";

// Get VM by ID
export async function getVmById(vmId: string) {
  const result = await db.select()
    .from(developersVms)
    .where(eq(developersVms.id, vmId))
    .limit(1);
  
  return result[0] || null;
}

// Get provider by ID
export async function getProviderById(providerId: string) {
  const result = await db.select()
    .from(providerResources)
    .where(eq(providerResources.id, providerId))
    .limit(1);
  
  return result[0] || null;
}

// Update VM status
export async function updateVmStatusById(vmId: string, status: string) {
  await db.update(developersVms)
    .set({ 
      status: status as any, // Cast to match enum type
      updatedAt: new Date().toISOString()
    })
    .where(eq(developersVms.id, vmId));
}

// Delete VM by ID
export async function deleteVmById(vmId: string) {
  await db.delete(developersVms)
    .where(eq(developersVms.id, vmId));
}
