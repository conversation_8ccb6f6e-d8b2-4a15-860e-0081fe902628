import { developers as developerSchema } from "../../drizzle/schema.ts";
import { db } from "../db.ts";
import { eq } from "drizzle-orm";

// Insert Developer
export async function insertDeveloper(
  userId: string,
  name: string,
  walletAddress: string,
) {
  return db.insert(developerSchema).values({
    userId,
    name,
    walletAddress,
  });
}

// Get Developer by ID
export async function getDeveloperById(developerId: string) {
  return db.select().from(developerSchema).where(
    eq(developerSchema.id, developerId),
  );
}

// Update Developer
export async function updateDeveloper(
  developerId: string,
  updates: Partial<typeof developerSchema>,
) {
  return db.update(developerSchema).set(updates).where(
    eq(developerSchema.id, developerId),
  );
}

// Delete Developer
export async function deleteDeveloper(developerId: string) {
  return db.delete(developerSchema).where(eq(developerSchema.id, developerId));
}

// Get Developer by User ID
export async function getDeveloperByUserId(userId: string) {
  const result = await db.select().from(developerSchema).where(
    eq(developerSchema.userId, userId),
  );
  return result[0] || null;
}

// Update Developer SSH Key
export async function updateDeveloperSSHKey(
  developerId: string,
  sshPublicKey: string,
) {
  return db.update(developerSchema).set({
    sshPublicKey,
    updatedAt: new Date().toISOString(),
  }).where(eq(developerSchema.id, developerId));
}
