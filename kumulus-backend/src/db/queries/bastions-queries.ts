import {
  bastions as bastionSchema,
  tunnels as tunnelSchema,
} from "../../drizzle/schema.ts";
import { db } from "../db.ts";
import { and, desc, eq, gt, gte, lt, or } from "drizzle-orm";



// Register Bastion
export async function registerBastion(
  name: string,
  ipAddress: string,
  portRangeStart: number,
  portRangeEnd: number,
  sshPublicKey?: string,
) {
  return db.insert(bastionSchema).values({
    name,
    ipAddress,
    portRangeStart,
    portRangeEnd,
    sshPublicKey,
  });
}

// Retrieve ipAddress of a bastion that has available ports and is active
export async function getAnActiveBastion() {
  return db.select({
    id: bastionSchema.id,
    ip: bastionSchema.ipAddress,
    sshPublicKey: bastionSchema.sshPublicKey,
  }).from(
    bastionSchema,
  )
    .where(
      and(
        eq(bastionSchema.isActive, true),
      ),
    ).orderBy(bastionSchema.portRangeStart).limit(1);
}

// Retrieve next available agent port (between 7777 and 7877)
// Check the highest port in use OR reserved and return the next one
export async function getAgentPort(bastionId: string) {
  const maxPortInUse = await db.select({ port: tunnelSchema.assignedPort })
    .from(tunnelSchema)
    .where(
      and(
        eq(tunnelSchema.bastionId, bastionId),
        eq(tunnelSchema.tunnelType, "agent"),
        gte(tunnelSchema.assignedPort, 7777),
        lt(tunnelSchema.assignedPort, 7878),
        // Check for both active AND reserved tunnels to prevent conflicts
        or(
          eq(tunnelSchema.status, "active"),
          eq(tunnelSchema.status, "reserved")
        )
      ),
    )
    .orderBy(desc(tunnelSchema.assignedPort)) // Order by port DESC to get highest first
    .limit(1);

  if (maxPortInUse.length === 0) {
    return 7777; // First agent gets port 7777
  } else {
    const nextPort = maxPortInUse[0].port + 1;
    if (nextPort > 7877) {
      throw new Error(
        "No available agent ports on this bastion (7777-7877 range full)",
      );
    }
    return nextPort;
  }
}

// Reserve a tunnel port for a resource (prevents race conditions)
export async function reserveTunnelPort(
  bastionId: string,
  resourceId: string,
  assignedPort: number
) {
  return db.insert(tunnelSchema).values({
    bastionId,
    providerContainerId: resourceId, // Using resourceId as container identifier
    assignedPort,
    tunnelType: "agent",
    status: "reserved", // Reserved but not yet active
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }).returning({ id: tunnelSchema.id });
}

// Activate a reserved tunnel (called when agent connects)
export async function activateReservedTunnel(resourceId: string) {
  return db.update(tunnelSchema)
    .set({
      status: "active",
      updatedAt: new Date().toISOString(),
    })
    .where(
      and(
        eq(tunnelSchema.providerContainerId, resourceId),
        eq(tunnelSchema.status, "reserved")
      )
    );
}

// Get next available VM SSH tunnel port for a bastion
export async function getVmSshPort(bastionId: string): Promise<number> {
  // Get bastion info
  const bastion = await db.select()
    .from(bastionSchema)
    .where(eq(bastionSchema.id, bastionId))
    .limit(1);

  if (!bastion || bastion.length === 0) {
    throw new Error("Bastion not found");
  }

  const bastionInfo = bastion[0];

  // VM SSH ports use a different range (18000-18999) to avoid conflicts with agent ports (7777-7877)
  const VM_SSH_PORT_START = 18000;
  const VM_SSH_PORT_END = 18999;

  // Get all existing VM SSH tunnel ports for this bastion
  const existingTunnels = await db.select({ assignedPort: tunnelSchema.assignedPort })
    .from(tunnelSchema)
    .where(
      and(
        eq(tunnelSchema.bastionId, bastionId),
        eq(tunnelSchema.tunnelType, "vm_ssh"),
        or(
          eq(tunnelSchema.status, "active"),
          eq(tunnelSchema.status, "reserved")
        )
      )
    );

  const usedPorts = new Set(existingTunnels.map(t => t.assignedPort));

  // Find next available port in VM SSH range
  for (let port = VM_SSH_PORT_START; port <= VM_SSH_PORT_END; port++) {
    if (!usedPorts.has(port)) {
      return port;
    }
  }

  throw new Error("No available VM SSH ports in range 18000-18999");
}

// Reserve a VM SSH tunnel port
export async function reserveVmSshTunnel(
  bastionId: string,
  vmContainerId: string,
  assignedPort: number,
  sshUsername: string
) {
  return db.insert(tunnelSchema).values({
    bastionId,
    providerContainerId: vmContainerId,
    assignedPort,
    sshUsername,
    tunnelType: "vm_ssh",
    status: "reserved",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }).returning({ id: tunnelSchema.id });
}

// Activate a VM SSH tunnel (called when tunnel is established)
export async function activateVmSshTunnel(vmContainerId: string) {
  return db.update(tunnelSchema)
    .set({
      status: "active",
      updatedAt: new Date().toISOString(),
    })
    .where(
      and(
        eq(tunnelSchema.providerContainerId, vmContainerId),
        eq(tunnelSchema.tunnelType, "vm_ssh"),
        eq(tunnelSchema.status, "reserved")
      )
    );
}

// Get VM SSH tunnel info
export async function getVmSshTunnelInfo(vmContainerId: string) {
  return db.select({
    id: tunnelSchema.id,
    bastionId: tunnelSchema.bastionId,
    assignedPort: tunnelSchema.assignedPort,
    sshUsername: tunnelSchema.sshUsername,
    status: tunnelSchema.status,
    createdAt: tunnelSchema.createdAt,
  })
    .from(tunnelSchema)
    .innerJoin(bastionSchema, eq(tunnelSchema.bastionId, bastionSchema.id))
    .where(
      and(
        eq(tunnelSchema.providerContainerId, vmContainerId),
        eq(tunnelSchema.tunnelType, "vm_ssh")
      )
    )
    .limit(1);
}
