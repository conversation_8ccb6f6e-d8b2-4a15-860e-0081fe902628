import { userSettings as userSettingsSchema } from "../../drizzle/schema.ts";
import { db } from "../db.ts";
import { eq } from "drizzle-orm";

// Get user settings
export async function getUserSettings(userId: string) {
  const result = await db.select().from(userSettingsSchema).where(
    eq(userSettingsSchema.userId, userId),
  );

  if (result.length === 0) {
    // Create default settings if none exist
    await createDefaultUserSettings(userId);
    return await db.select().from(userSettingsSchema).where(
      eq(userSettingsSchema.userId, userId),
    );
  }

  return result;
}

// Create default user settings
export async function createDefaultUserSettings(userId: string) {
  return db.insert(userSettingsSchema).values({
    userId,
    notifications: {
      email: true,
      push: false,
    },
    privacy: {
      profileVisible: true,
      activityVisible: false,
    },
    security: {
      twoFactorEnabled: false,
      lastPasswordChange: null,
    },
    preferences: {
      theme: "dark",
      language: "en",
      timezone: "UTC",
    },
  });
}

// Update user settings
export async function updateUserSettings(
  userId: string,
  settings: {
    notifications?: any;
    privacy?: any;
    security?: any;
    preferences?: any;
  },
) {
  return db.update(userSettingsSchema)
    .set({
      notifications: settings.notifications,
      privacy: settings.privacy,
      security: settings.security,
      preferences: settings.preferences,
      updatedAt: new Date().toISOString(),
    })
    .where(eq(userSettingsSchema.userId, userId));
}
