import { developerVms as VMSchema } from "../../drizzle/schema.ts";
import { db } from "../db.ts";
import { eq } from "drizzle-orm";

// Insert VM
export async function insertVM(
  developerId: string,
  providerResourceId: string,
  containerId: string,
  ram: number,
  cpuCores: number,
  storage: number,
  gpu: any,
  sshPublicKey: string,
  sshPort: number,
) {
  return db.insert(VMSchema).values({
    developerId,
    providerResourceId,
    containerId,
    ram,
    cpuCores,
    storage,
    gpu,
    sshPublicKey,
    sshPort,
  });
}

// Get VM by ID
export async function getVMById(vmId: string) {
  return db.select().from(VMSchema).where(eq(VMSchema.id, vmId));
}

// Update VM
export async function updateVM(
  vmId: string,
  updates: Partial<typeof VMSchema>,
) {
  return db.update(VMSchema).set(updates).where(eq(VMSchema.id, vmId));
}

// Delete VM
export async function deleteVM(vmId: string) {
  return db.delete(VMSchema).where(eq(VMSchema.id, vmId));
}

// Get VM by Container ID
export async function getVMByContainerId(containerId: string) {
  return db.select().from(VMSchema).where(eq(VMSchema.containerId, containerId))
    .limit(1);
}

// Update VM Status
export async function updateVMStatus(containerId: string, status: string) {
  return db.update(VMSchema).set({ status }).where(
    eq(VMSchema.containerId, containerId),
  );
}

// Insert Developer VM (alias for insertVM with object parameter)
export async function insertDeveloperVm(vmData: {
  developerId: string;
  providerResourceId: string;
  containerId: string;
  name: string;
  ram: number;
  cpuCores: number;
  storage: number;
  status: string;
  sshPublicKey: string;
  sshPort: number;
}) {
  return db.insert(VMSchema).values(vmData);
}

// Get Developer VM by Container ID (alias)
export async function getDeveloperVmByContainerId(containerId: string) {
  const result = await db.select().from(VMSchema).where(
    eq(VMSchema.containerId, containerId),
  ).limit(1);
  return result[0] || null;
}

// Update VM Status (alias)
export async function updateVmStatus(containerId: string, status: string) {
  return db.update(VMSchema).set({ status }).where(
    eq(VMSchema.containerId, containerId),
  );
}

// Get VMs by Developer ID
export async function getVMsByDeveloperId(developerId: string) {
  return db.select().from(VMSchema).where(
    eq(VMSchema.developerId, developerId),
  );
}
