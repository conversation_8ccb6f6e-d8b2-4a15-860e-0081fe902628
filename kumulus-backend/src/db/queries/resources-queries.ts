import { providerResources as resourceSchema } from "../../drizzle/schema.ts";
import { db } from "../db.ts";
import { and, eq, gte } from "drizzle-orm";

// Register resource
export function registerResource(
  name: string,
  providerId: string,
  cpuCores: number,
  ram: number,
  storage: number,
  gpu: any,
  ipAddress?: string, // Optional bastion tunnel endpoint
) {
  return db.insert(resourceSchema).values({
    name,
    providerId,
    cpuCores,
    ram,
    storage,
    gpu,
    ipAddress, // Store the bastion tunnel endpoint
  }).returning({ id: resourceSchema.id });
}

// Get resource by ID
export async function getResourceById(resourceId: string) {
  return db.select().from(resourceSchema).where(
    eq(resourceSchema.id, resourceId),
  );
}

// Update resource
export async function updateResource(
  resourceId: string,
  updates: Partial<typeof resourceSchema>,
) {
  return db.update(resourceSchema).set(updates).where(
    eq(resourceSchema.id, resourceId),
  );
}

// Delete resource
export async function deleteResource(resourceId: string) {
  return db.delete(resourceSchema).where(eq(resourceSchema.id, resourceId));
}

// Get all resources for a provider
export async function getResourcesByProviderId(providerId: string) {
  return db.select().from(resourceSchema).where(
    eq(resourceSchema.providerId, providerId),
  );
}

// get randomly one resource id that has specs and it's ip address
export async function getRandomResource(
  cpu: number,
  ram: number,
  storage: number,
) {
  return db.select({
    id: resourceSchema.id,
    specs: resourceSchema.checkedSpecs,
  })
    .from(resourceSchema).where(
      and(
        gte(resourceSchema.cpuCores, cpu),
        gte(resourceSchema.ram, ram),
        gte(resourceSchema.storage, storage),
        eq(resourceSchema.status, "ready"),
      ),
    ).orderBy(resourceSchema.cpuCores).limit(1);
}
