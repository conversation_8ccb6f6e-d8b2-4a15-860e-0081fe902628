import { APP_TYPES, VM_STATUS, DEPLOYMENT_STATUS } from "@/app/constants";

// Auth Types
export interface User {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Session {
  id: string;
  userId: string;
  expiresAt: string;
  token: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface AuthSession {
  user: User | null;
  session: Session | null;
}

// VM Types
export interface CreateVMRequest {
  name: string;
  username: string;
  sshKey: string;
  cpu: number;
  memory: string; // Format: "2g", "4g", etc.
  disk: string;   // Format: "50g", "100g", etc.
}

export interface CreateVMResponse {
  vmId: string;
  status: string;
  sshPort: number;
  message?: string;
  bastionIp?: string;
  bastionPort?: number;
  sshAccess?: string;
  tunnelStatus?: string;
  warning?: string;
  error?: string;
}

export interface VM {
  id: string;
  developerId: string;
  providerResourceId: string;
  containerId: string;
  name: string;
  ram: number;
  cpuCores: number;
  storage: number;
  gpu?: any;
  status: keyof typeof VM_STATUS;
  sshPublicKey: string;
  sshPort: number;
  createdAt: string;
  updatedAt: string;
  // SSH tunnel information
  sshTunnel?: {
    bastionIp: string;
    bastionPort: number;
    sshAccess: string;
    status: string;
  };
}

export interface VMControlRequest {
  vmId: string;
}

// App Types
export type AppType = typeof APP_TYPES[number];

export interface CreateAppRequest {
  appType: AppType;
  cpu: number;
  memory: string; // Format: "2g", "4g", etc.
  devSSHKey?: string;
}

export interface CreateAppResponse {
  deploymentId: string;
  networkName: string;
  containers: Array<{
    id: string;
    type: string;
    status: string;
    port?: number;
  }>;
}

export interface AppDeployment {
  id: string;
  developerId: string;
  providerResourceId: string;
  appType: AppType;
  networkName: string;
  status: keyof typeof DEPLOYMENT_STATUS;
  totalCpu: number;
  totalMemory: number;
  createdAt: string;
  updatedAt: string;
}

export interface DeploymentControlRequest {
  deploymentId: string;
}

// Dashboard Types
export interface DashboardStats {
  totalVMs: number;
  runningVMs: number;
  stoppedVMs: number;
  totalResources: number;
}

export interface DashboardData {
  user: User;
  stats: DashboardStats;
}

// Profile Types
export interface ProfileData {
  profile: User & {
    sshPublicKey?: string;
  };
}

export interface UpdateProfileRequest {
  name?: string;
  image?: string;
  sshPublicKey?: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  details?: any;
}

export interface ApiError {
  error: string;
  message?: string;
  details?: any;
  status?: number;
}

// Provider Types (for testing)
export interface Provider {
  id: string;
  ip: string;
  resourceId: string;
  name?: string;
  status?: string;
}
