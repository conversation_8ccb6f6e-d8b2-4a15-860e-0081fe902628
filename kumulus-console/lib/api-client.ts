import { BACKEND_URL, API_ENDPOINTS } from "@/app/constants";
import type {
  CreateVMRequest,
  CreateVMResponse,
  VMControlRequest,
  CreateAppRequest,
  CreateAppResponse,
  DeploymentControlRequest,
  DashboardData,
  ProfileData,
  UpdateProfileRequest,
  Provider,
  ApiResponse,
  ApiError,
  VM,
  AppDeployment,
} from "./types";

// Base API client class
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error: ApiError = {
          error: errorData.error || "Request failed",
          message: errorData.message || `HTTP ${response.status}`,
          details: errorData.details,
          status: response.status,
        };
        throw error;
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && 'status' in error) {
        throw error; // Re-throw API errors
      }
      
      // Handle network errors
      const networkError: ApiError = {
        error: "Network Error",
        message: error instanceof Error ? error.message : "Failed to connect to server",
      };
      throw networkError;
    }
  }

  // Authentication methods
  async getSession(): Promise<any> {
    return this.request(API_ENDPOINTS.AUTH.SESSION);
  }

  async getToken(): Promise<{ token: string }> {
    return this.request(API_ENDPOINTS.AUTH.TOKEN);
  }

  // Dashboard methods
  async getDashboardOverview(): Promise<DashboardData> {
    return this.request(API_ENDPOINTS.DASHBOARD.OVERVIEW);
  }

  // Profile methods
  async getProfile(): Promise<ProfileData> {
    return this.request(API_ENDPOINTS.PROFILE.GET);
  }

  async updateProfile(data: UpdateProfileRequest): Promise<ProfileData> {
    return this.request(API_ENDPOINTS.PROFILE.UPDATE, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  // Developer methods
  async linkDeveloper(userId: string): Promise<ApiResponse> {
    return this.request(API_ENDPOINTS.DEVELOPERS.LINK, {
      method: "POST",
      body: JSON.stringify({ userId }),
    });
  }

  // VM methods
  async getVMs(): Promise<{ vms: VM[]; total: number }> {
    return this.request(API_ENDPOINTS.VMS.LIST);
  }

  async createVM(data: CreateVMRequest): Promise<CreateVMResponse> {
    return this.request(API_ENDPOINTS.VMS.CREATE, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async startVM(vmId: string): Promise<ApiResponse> {
    return this.request(API_ENDPOINTS.VMS.START, {
      method: "POST",
      body: JSON.stringify({ vmId }),
    });
  }

  async stopVM(vmId: string): Promise<ApiResponse> {
    return this.request(API_ENDPOINTS.VMS.STOP, {
      method: "POST",
      body: JSON.stringify({ vmId }),
    });
  }

  async deleteVM(vmId: string): Promise<ApiResponse> {
    return this.request(API_ENDPOINTS.VMS.DELETE, {
      method: "POST",
      body: JSON.stringify({ vmId }),
    });
  }

  async testVMProvider(): Promise<Provider> {
    return this.request(API_ENDPOINTS.VMS.TEST_PROVIDER);
  }

  // App methods
  async getApps(): Promise<{ apps: AppDeployment[]; total: number }> {
    return this.request(API_ENDPOINTS.APPS.LIST);
  }

  async createApp(data: CreateAppRequest): Promise<CreateAppResponse> {
    return this.request(API_ENDPOINTS.APPS.CREATE, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async startApp(data: DeploymentControlRequest): Promise<ApiResponse> {
    return this.request(API_ENDPOINTS.APPS.START, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async stopApp(data: DeploymentControlRequest): Promise<ApiResponse> {
    return this.request(API_ENDPOINTS.APPS.STOP, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async deleteApp(data: DeploymentControlRequest): Promise<ApiResponse> {
    return this.request(API_ENDPOINTS.APPS.DELETE, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async testAppProvider(): Promise<Provider> {
    return this.request(API_ENDPOINTS.APPS.TEST_PROVIDER);
  }
}

// Create and export the API client instance
export const apiClient = new ApiClient(BACKEND_URL);

// Export utility functions for data transformation
export const transformVMFormData = (formData: {
  name: string;
  cpu: number;
  memory: number; // in GB
  disk: number;   // in GB
  username: string;
  sshKey: string;
}): CreateVMRequest => ({
  name: formData.name,
  username: formData.username,
  sshKey: formData.sshKey,
  cpu: formData.cpu,
  memory: `${formData.memory}g`,
  disk: `${formData.disk}g`,
});

export const transformAppFormData = (formData: {
  appType: string;
  cpu: number;
  memory: number; // in GB
  devSSHKey?: string;
}): CreateAppRequest => ({
  appType: formData.appType as any,
  cpu: formData.cpu,
  memory: `${formData.memory}g`,
  devSSHKey: formData.devSSHKey,
});

// Error handling utilities
export const isApiError = (error: any): error is ApiError => {
  return error && typeof error === 'object' && 'error' in error;
};

export const getErrorMessage = (error: unknown): string => {
  if (isApiError(error)) {
    return error.message || error.error;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return "An unexpected error occurred";
};
