"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { DashboardLayout } from "@/components/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  ArrowLeft,
  Check,
  Copy,
  Cpu,
  HardDrive,
  Key,
  MemoryStick,
  Monitor,
  Server,
  Terminal,
} from "lucide-react";
import Link from "next/link";
import { apiClient, transformVMFormData, getErrorMessage } from "@/lib/api-client";
import { useSession } from "@/lib/auth-client";
import { CreateVMResponse } from "@/lib/types";

interface VMSize {
  id: string;
  name: string;
  vcpus: number;
  memory: number; // in MB
  disk: number; // in GB
  transfer: number; // in TB
  price: {
    hourly: number;
    monthly: number;
  };
  popular?: boolean;
}

interface UbuntuImage {
  id: string;
  name: string;
  version: string;
  description: string;
  recommended?: boolean;
}

const VM_SIZES: VMSize[] = [
  {
    id: "s-1vcpu-2gb",
    name: "Basic",
    vcpus: 1,
    memory: 2048,
    disk: 30,
    transfer: 2,
    price: { hourly: 120, monthly: 900 },
  },
  {
    id: "s-2vcpu-4gb",
    name: "Standard",
    vcpus: 2,
    memory: 4096,
    disk: 70,
    transfer: 4,
    price: { hourly: 240, monthly: 1800 },
    popular: true,
  },
  {
    id: "s-4vcpu-8gb",
    name: "Performance",
    vcpus: 4,
    memory: 8192,
    disk: 120,
    transfer: 5,
    price: { hourly: 480, monthly: 3600 },
  },
];

const UBUNTU_IMAGES: UbuntuImage[] = [
  {
    id: "ubuntu-22-04-x64",
    name: "Ubuntu 22.04 LTS",
    version: "22.04",
    description: "Previous LTS release, stable and well-tested",
  }
];



export default function CreateVMPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [selectedImage, setSelectedImage] = useState<string>(UBUNTU_IMAGES[0].id);
  const [selectedSize, setSelectedSize] = useState<string>(VM_SIZES[1].id);
  const [vmName, setVmName] = useState<string>("");
  const [isCreating, setIsCreating] = useState(false);
  const [hasSSHKey, setHasSSHKey] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [vmCreated, setVmCreated] = useState<CreateVMResponse | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const selectedSizeData = VM_SIZES.find(size => size.id === selectedSize);
  const selectedImageData = UBUNTU_IMAGES.find(image => image.id === selectedImage);

  // Check for SSH key on component mount
  useEffect(() => {
    const checkSSHKey = async () => {
      try {
        const profile = await apiClient.getProfile();
        setHasSSHKey(!!profile.profile.sshPublicKey && profile.profile.sshPublicKey.trim().length > 0);
      } catch (error) {
        console.error("Failed to fetch profile:", error);
        setHasSSHKey(false);
      }
    };

    if (session?.user) {
      checkSSHKey();
    }
  }, [session]);

  const handleCreateVM = async () => {
    if (!vmName.trim()) {
      setError("Please enter a VM name");
      return;
    }

    if (!hasSSHKey) {
      setError("Please add your SSH public key in your profile before creating a VM");
      return;
    }

    if (!selectedSizeData) {
      setError("Please select a VM size");
      return;
    }

    setIsCreating(true);
    setError("");

    try {
      // Get SSH key from profile
      const profile = await apiClient.getProfile();
      const sshKey = profile.profile.sshPublicKey;

      if (!sshKey) {
        throw new Error("SSH key not found in profile");
      }

      // Transform form data to API format
      const vmData = transformVMFormData({
        name: vmName,
        username: "ubuntu", // Default username for Ubuntu VMs
        sshKey: sshKey,
        cpu: selectedSizeData.vcpus,
        memory: selectedSizeData.memory / 1024, // Convert MB to GB
        disk: selectedSizeData.disk,
      });

      // Create VM via API
      const response = await apiClient.createVM(vmData);

      console.log("VM created successfully:", response);

      // Show success page with SSH access information
      setVmCreated(response);
      setShowSuccess(true);
    } catch (error) {
      console.error("Failed to create VM:", error);
      setError(getErrorMessage(error));
    } finally {
      setIsCreating(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // Show success page if VM was created
  if (showSuccess && vmCreated) {
    return (
      <DashboardLayout>
        <div className="space-y-6 max-w-3xl mx-auto">
          {/* Success Header */}
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Check className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold tracking-tight text-green-800">
              VM Created Successfully!
            </h1>
            <p className="text-muted-foreground mt-2">
              Your Ubuntu VM is now running and ready to use
            </p>
          </div>

          {/* VM Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                VM Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">VM ID</Label>
                  <p className="text-sm text-muted-foreground font-mono">{vmCreated.vmId}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <Badge className="ml-2">{vmCreated.status}</Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">SSH Port</Label>
                  <p className="text-sm text-muted-foreground">{vmCreated.sshPort}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Username</Label>
                  <p className="text-sm text-muted-foreground">ubuntu</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SSH Access Information */}
          {vmCreated.sshAccess && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Terminal className="h-5 w-5" />
                  SSH Access
                </CardTitle>
                <CardDescription>
                  Connect to your VM using SSH
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">SSH Command</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <code className="flex-1 p-2 bg-muted rounded text-sm font-mono">
                      {vmCreated.sshAccess}
                    </code>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(vmCreated.sshAccess!)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {vmCreated.bastionIp && vmCreated.bastionPort && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Bastion IP</Label>
                      <p className="text-sm text-muted-foreground font-mono">{vmCreated.bastionIp}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Bastion Port</Label>
                      <p className="text-sm text-muted-foreground">{vmCreated.bastionPort}</p>
                    </div>
                  </div>
                )}

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-800 mb-2">Connection Instructions:</h4>
                  <ol className="text-sm text-blue-700 space-y-1">
                    <li>1. Copy the SSH command above</li>
                    <li>2. Open your terminal</li>
                    <li>3. Paste and run the command</li>
                    <li>4. Accept the host key when prompted</li>
                  </ol>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Warning/Error Messages */}
          {vmCreated.warning && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <AlertCircle className="h-5 w-5 text-yellow-600" />
                  <div>
                    <h3 className="font-medium text-yellow-800">Warning</h3>
                    <p className="text-sm text-yellow-700">{vmCreated.warning}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {vmCreated.error && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <div>
                    <h3 className="font-medium text-red-800">SSH Setup Issue</h3>
                    <p className="text-sm text-red-700">{vmCreated.error}</p>
                    <p className="text-sm text-red-600 mt-1">
                      Your VM is running, but SSH tunnel setup encountered an issue.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className="flex gap-4 justify-center">
            <Link href="/vms">
              <Button>
                <Monitor className="h-4 w-4 mr-2" />
                View All VMs
              </Button>
            </Link>
            <Button
              variant="outline"
              onClick={() => {
                setShowSuccess(false);
                setVmCreated(null);
                setVmName("");
                setError("");
              }}
            >
              Create Another VM
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/vms">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to VMs
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create Virtual Machine</h1>
            <p className="text-muted-foreground">
              Deploy a new Ubuntu VM in seconds
            </p>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <div>
                  <h3 className="font-medium text-red-800">Error</h3>
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* SSH Key Warning */}
        {!hasSSHKey && (
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-amber-600" />
                <div>
                  <h3 className="font-medium text-amber-800">SSH Key Required</h3>
                  <p className="text-sm text-amber-700">
                    You need to add your SSH public key before creating VMs.{" "}
                    <Link href="/profile" className="underline font-medium">
                      Add SSH key in your profile
                    </Link>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Configuration */}
          <div className="lg:col-span-2 space-y-6">
            {/* VM Name */}
            <Card>
              <CardHeader>
                <CardTitle>VM Name</CardTitle>
                <CardDescription>
                  Choose a name for your virtual machine
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="vm-name">Name</Label>
                  <Input
                    id="vm-name"
                    placeholder="my-ubuntu-vm"
                    value={vmName}
                    onChange={(e) => setVmName(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    Use lowercase letters, numbers, and hyphens only
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Choose Image */}
            <Card>
              <CardHeader>
                <CardTitle>Choose an Image</CardTitle>
                <CardDescription>
                  Select the Ubuntu version for your virtual machine
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {UBUNTU_IMAGES.map((image) => (
                  <div
                    key={image.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedImage === image.id
                        ? "border-primary bg-primary/5"
                        : "border-border hover:border-primary/50"
                    }`}
                    onClick={() => setSelectedImage(image.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Monitor className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{image.name}</span>
                            {image.recommended && (
                              <Badge variant="secondary" className="text-xs">
                                Recommended
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {image.description}
                          </p>
                        </div>
                      </div>
                      {selectedImage === image.id && (
                        <Check className="h-5 w-5 text-primary" />
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Choose Size */}
            <Card>
              <CardHeader>
                <CardTitle>Choose a Size</CardTitle>
                <CardDescription>
                  Select the resources for your virtual machine
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {VM_SIZES.map((size) => (
                  <div
                    key={size.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedSize === size.id
                        ? "border-primary bg-primary/5"
                        : "border-border hover:border-primary/50"
                    }`}
                    onClick={() => setSelectedSize(size.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{size.name}</span>
                            {size.popular && (
                              <Badge className="text-xs bg-blue-100 text-blue-800 border-blue-200">
                                Popular
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center space-x-4 mt-1 text-sm text-muted-foreground">
                            <div className="flex items-center space-x-1">
                              <Cpu className="h-3 w-3" />
                              <span>{size.vcpus} vCPU{size.vcpus !== 1 ? "s" : ""}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <MemoryStick className="h-3 w-3" />
                              <span>{size.memory / 1024} GB</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <HardDrive className="h-3 w-3" />
                              <span>{size.disk} GB</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">$R {size.price.monthly}/mo</div>
                        <div className="text-sm text-muted-foreground">$R {size.price.hourly}/hr</div>
                        {selectedSize === size.id && (
                          <Check className="h-5 w-5 text-primary mt-1 ml-auto" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>


          </div>

          {/* Summary */}
          <div className="space-y-6">
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle>Summary</CardTitle>
                <CardDescription>Review your VM configuration</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="text-sm font-medium">Image</div>
                    <div className="text-sm text-muted-foreground">
                      {selectedImageData?.name}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium">Size</div>
                    <div className="text-sm text-muted-foreground">
                      {selectedSizeData?.name} - {selectedSizeData?.vcpus} vCPU, {selectedSizeData ? selectedSizeData.memory / 1024 : 0} GB RAM
                    </div>
                  </div>
                  {vmName && (
                    <div>
                      <div className="text-sm font-medium">Name</div>
                      <div className="text-sm text-muted-foreground font-mono">
                        {vmName}
                      </div>
                    </div>
                  )}
                  <div>
                    <div className="text-sm font-medium">SSH Access</div>
                    <div className="flex items-center gap-2">
                      {hasSSHKey ? (
                        <>
                          <Key className="h-3 w-3 text-green-600" />
                          <span className="text-sm text-green-600">SSH key configured</span>
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-3 w-3 text-amber-600" />
                          <span className="text-sm text-amber-600">SSH key required</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm">Monthly cost:</span>
                    <span className="font-semibold">$R {selectedSizeData?.price.monthly}/mo</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Hourly cost:</span>
                    <span className="text-sm text-muted-foreground">$R {selectedSizeData?.price.hourly}/hr</span>
                  </div>
                </div>

                <Button
                  className="w-full"
                  onClick={handleCreateVM}
                  disabled={!vmName.trim() || isCreating || !hasSSHKey}
                >
                  {isCreating ? (
                    <>
                      <Server className="h-4 w-4 mr-2 animate-pulse" />
                      Creating VM...
                    </>
                  ) : (
                    <>
                      <Server className="h-4 w-4 mr-2" />
                      Create VM
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
