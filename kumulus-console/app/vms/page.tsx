"use client";

import { useEffect, useState } from "react";
import { DashboardLayout } from "@/components/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Activity,
  AlertCircle,
  Clock,
  Cpu,
  HardDrive,
  Key,
  MemoryStick,
  Monitor,
  Plus,
  Power,
  RefreshCw,
  Server,
  Square,
} from "lucide-react";
import Link from "next/link";
import { apiClient, getErrorMessage } from "@/lib/api-client";
import { useSession } from "@/lib/auth-client";
import type { VM as BackendVM } from "@/lib/types";
import { VMDetailsModal } from "@/components/vm-details-modal";

interface VM {
  id: string;
  name: string;
  status: "running" | "stopped" | "creating" | "starting" | "stopping" | "error";
  image: string;
  size: {
    vcpus: number;
    memory: number;
    disk: number;
  };
  ipAddress?: string;
  region: string;
  createdAt: string;
  cost: {
    hourly: number;
    monthly: number;
  };
  sshAccess?: string;
  bastionIp?: string;
  bastionPort?: number;
}

interface VMsData {
  vms: VM[];
  total: number;
  running: number;
  stopped: number;
}

export default function VMsPage() {
  const { data: session } = useSession();
  const [vmsData, setVmsData] = useState<VMsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasSSHKey, setHasSSHKey] = useState<boolean>(false);
  const [selectedVM, setSelectedVM] = useState<VM | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const fetchVMs = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch VMs from backend API
      const response = await apiClient.getVMs();

      // Transform backend VM data to frontend format
      const transformedVMs: VM[] = response.vms.map((vm: BackendVM) => {
        // Generate a user-friendly name if not available
        const vmName = vm.name && vm.name !== vm.containerId
          ? vm.name
          : `VM-${vm.containerId.slice(-8)}`; // Use last 8 chars of container ID

        return {
          id: vm.containerId,
          name: vmName,
          status: vm.status as any,
          image: "Ubuntu 24.04 LTS", // Default image
          size: {
            vcpus: vm.cpuCores,
            memory: vm.ram,
            disk: vm.storage,
          },
          ipAddress: undefined, // Not available in current backend response
          region: "auto", // Auto-selected region
          createdAt: vm.createdAt,
          cost: {
            hourly: 0.012, // Mock pricing for now
            monthly: 9.00,
          },
          // Add SSH tunnel info if available
          bastionIp: vm.sshTunnel?.bastionIp,
          bastionPort: vm.sshTunnel?.bastionPort,
          sshAccess: vm.sshTunnel?.sshAccess,
        };
      });

      const vmsData: VMsData = {
        vms: transformedVMs,
        total: transformedVMs.length,
        running: transformedVMs.filter(vm => vm.status === "running").length,
        stopped: transformedVMs.filter(vm => vm.status === "stopped").length,
      };

      setVmsData(vmsData);
    } catch (err) {
      console.error("Failed to fetch VMs:", err);
      setError(getErrorMessage(err));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      fetchVMs();

      // Check for SSH key from profile
      const checkSSHKey = async () => {
        try {
          const profile = await apiClient.getProfile();
          setHasSSHKey(!!profile.profile.sshPublicKey && profile.profile.sshPublicKey.trim().length > 0);
        } catch (error) {
          console.error("Failed to fetch profile:", error);
          // Fallback to localStorage
          const sshKey = localStorage.getItem("kumulus-dev-ssh-key");
          setHasSSHKey(!!sshKey && sshKey.trim().length > 0);
        }
      };

      checkSSHKey();
    }
  }, [session]);

  // Format memory display (input is in GB from backend)
  const formatMemory = (memoryGB: number): string => {
    if (memoryGB >= 1) {
      return `${memoryGB} GB RAM`;
    } else {
      // Convert to MB for values less than 1 GB
      const memoryMB = Math.round(memoryGB * 1024);
      return `${memoryMB} MB RAM`;
    }
  };

  const handleVMClick = (vm: VM) => {
    setSelectedVM(vm);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedVM(null);
  };

  const handleVMUpdate = () => {
    fetchVMs(); // Refresh the VM list
  };

  const getStatusColor = (status: VM["status"]) => {
    switch (status) {
      case "running":
        return "bg-green-100 text-green-800 border-green-200";
      case "stopped":
        return "bg-gray-100 text-gray-800 border-gray-200";
      case "creating":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "starting":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "stopping":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "error":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: VM["status"]) => {
    switch (status) {
      case "running":
        return <Power className="h-3 w-3" />;
      case "stopped":
        return <Square className="h-3 w-3" />;
      case "creating":
      case "starting":
      case "stopping":
        return <RefreshCw className="h-3 w-3 animate-spin" />;
      case "error":
        return <AlertCircle className="h-3 w-3" />;
      default:
        return <Square className="h-3 w-3" />;
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading VMs...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error && !vmsData) {
    return (
      <DashboardLayout>
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading VMs
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchVMs} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </DashboardLayout>
    );
  }

  if (!vmsData || vmsData.total === 0) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Virtual Machines</h1>
              <p className="text-muted-foreground">
                Create and manage your Ubuntu VMs
              </p>
            </div>
            {hasSSHKey ? (
              <Link href="/vms/create">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create VM
                </Button>
              </Link>
            ) : (
              <Link href="/profile">
                <Button variant="outline">
                  <Key className="h-4 w-4 mr-2" />
                  Add SSH Key First
                </Button>
              </Link>
            )}
          </div>

          {/* Empty State */}
          <Card className="text-center py-12">
            <CardContent>
              <Monitor className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">
                No virtual machines
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Get started by creating your first Ubuntu virtual machine.
                Choose from different sizes and configurations to match your needs.
              </p>
              {!hasSSHKey ? (
                <div className="space-y-4">
                  <div className="p-4 bg-amber-50 border border-amber-200 rounded-md max-w-md mx-auto">
                    <p className="text-sm text-amber-800">
                      <strong>SSH Key Required:</strong> Add your SSH public key in your profile before creating VMs.
                    </p>
                  </div>
                  <div className="flex gap-2 justify-center">
                    <Link href="/profile">
                      <Button variant="outline">
                        <Key className="h-4 w-4 mr-2" />
                        Add SSH Key
                      </Button>
                    </Link>
                    <Link href="/vms/create">
                      <Button disabled>
                        <Plus className="h-4 w-4 mr-2" />
                        Create VM
                      </Button>
                    </Link>
                  </div>
                </div>
              ) : (
                <Link href="/vms/create">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create VM
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Virtual Machines</h1>
            <p className="text-muted-foreground">
              {vmsData.total} VM{vmsData.total !== 1 ? "s" : ""} • {vmsData.running} running • {vmsData.stopped} stopped
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={fetchVMs} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Link href="/vms/create">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create VM
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total VMs</CardTitle>
              <Monitor className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{vmsData.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Running</CardTitle>
              <Activity className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{vmsData.running}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Cost</CardTitle>
              <Server className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${vmsData.vms.reduce((total, vm) => total + vm.cost.monthly, 0).toFixed(2)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* VMs List */}
        <div className="grid gap-4">
          {vmsData.vms.map((vm) => (
            <Card key={vm.id} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleVMClick(vm)}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <Monitor className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{vm.name}</h3>
                      <p className="text-sm text-muted-foreground">{vm.image}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <Badge className={getStatusColor(vm.status)}>
                      {getStatusIcon(vm.status)}
                      <span className="ml-1 capitalize">{vm.status}</span>
                    </Badge>
                    <div className="text-right">
                      <p className="text-sm font-medium">${vm.cost.monthly}/mo</p>
                      <p className="text-xs text-muted-foreground">${vm.cost.hourly}/hr</p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Cpu className="h-4 w-4 text-muted-foreground" />
                    <span>{vm.size.vcpus} vCPU{vm.size.vcpus !== 1 ? "s" : ""}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MemoryStick className="h-4 w-4 text-muted-foreground" />
                    <span>{formatMemory(vm.size.memory)}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <HardDrive className="h-4 w-4 text-muted-foreground" />
                    <span>{vm.size.disk} GB SSD</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{new Date(vm.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>

                {vm.ipAddress && (
                  <div className="mt-2">
                    <p className="text-sm text-muted-foreground">
                      IP Address: <span className="font-mono">{vm.ipAddress}</span>
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* VM Details Modal */}
      <VMDetailsModal
        vm={selectedVM}
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onVMUpdate={handleVMUpdate}
      />
    </DashboardLayout>
  );
}
