export const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication (handled by BetterAuth)
  AUTH: {
    SESSION: "/api/auth/session",
    TOKEN: "/api/auth/token",
  },
  
  // Dashboard
  DASHBOARD: {
    OVERVIEW: "/api/dashboard/overview",
  },
  
  // Profile
  PROFILE: {
    GET: "/api/profile",
    UPDATE: "/api/profile",
  },

  // Developers
  DEVELOPERS: {
    LINK: "/api/developers/linkdeveloper",
  },
  
  // VMs
  VMS: {
    LIST: "/api/vms",
    CREATE: "/api/vms/create-vm",
    START: "/api/vms/start",
    STOP: "/api/vms/stop",
    DELETE: "/api/vms/delete-vm",
    TEST_PROVIDER: "/api/vms/test-provider",
  },
  
  // Apps
  APPS: {
    LIST: "/api/apps",
    CREATE: "/api/apps/create-app",
    START: "/api/apps/start-app",
    STOP: "/api/apps/stop-app", 
    DELETE: "/api/apps/delete-app",
    TEST_PROVIDER: "/api/apps/test-provider",
  },
} as const;

// App Types supported by backend
export const APP_TYPES = ["odoo", "wordpress", "libreoffice", "nextcloud"] as const;

// VM Status Types
export const VM_STATUS = {
  CREATING: "creating",
  RUNNING: "running",
  STOPPED: "stopped",
  STARTING: "starting",
  STOPPING: "stopping",
  DELETED: "deleted",
  ERROR: "error",
} as const;

// App Deployment Status Types
export const DEPLOYMENT_STATUS = {
  CREATING: "creating",
  RUNNING: "running",
  STOPPED: "stopped", 
  FAILED: "failed",
  DELETED: "deleted",
} as const;
