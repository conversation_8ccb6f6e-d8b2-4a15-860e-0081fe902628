"use client";

import { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Activity,
  Clock,
  Monitor,
  Plus,
  RefreshCw,
  Server,
  TrendingUp,
} from "lucide-react";
import Link from "next/link";
import { apiClient, getErrorMessage } from "@/lib/api-client";
import { useSession } from "@/lib/auth-client";
import type { DashboardData } from "@/lib/types";

interface LocalDashboardData {
  stats: {
    totalVMs: number;
    runningVMs: number;
    stoppedVMs: number;
    totalResources: number;
  };
  recentActivity: Array<{
    id: number;
    action: string;
    resource: string;
    timestamp: string;
    status: "success" | "error" | "info";
  }>;
}

export default function DashboardPage() {
  const { data: session } = useSession();
  const [dashboardData, setDashboardData] = useState<LocalDashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch dashboard data from backend
      const response = await apiClient.getDashboardOverview();

      // Transform backend data to local format
      const localData: LocalDashboardData = {
        stats: {
          totalVMs: response.stats.totalVMs || 0,
          runningVMs: response.stats.runningVMs || 0,
          stoppedVMs: response.stats.stoppedVMs || 0,
          totalResources: response.stats.totalResources || 0,
        },
        recentActivity: response.recentActivity || [
          {
            id: 1,
            action: "Dashboard loaded",
            resource: "System",
            timestamp: new Date().toISOString(),
            status: "success",
          },
        ],
      };

      setDashboardData(localData);
    } catch (err) {
      console.error("Failed to fetch dashboard data:", err);
      setError(getErrorMessage(err));

      // Fallback to mock data on error
      const fallbackData: LocalDashboardData = {
        stats: {
          totalVMs: 0,
          runningVMs: 0,
          stoppedVMs: 0,
          totalResources: 0,
        },
        recentActivity: [],
      };
      setDashboardData(fallbackData);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      fetchDashboardData();
    }
  }, [session]);

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-blue-900">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back, {session?.user?.name}
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={fetchDashboardData} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Link href="/vms/create">
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                Create VM
              </Button>
            </Link>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-3">
                <Activity className="h-5 w-5 text-red-600" />
                <div>
                  <h3 className="font-medium text-red-800">Error Loading Dashboard</h3>
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total VMs</CardTitle>
              <Monitor className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900">
                {dashboardData?.stats.totalVMs}
              </div>
              <p className="text-xs text-muted-foreground">
                Virtual machines deployed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Running VMs</CardTitle>
              <Activity className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {dashboardData?.stats.runningVMs}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Stopped VMs</CardTitle>
              <Server className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-600">
                {dashboardData?.stats.stoppedVMs}
              </div>
              <p className="text-xs text-muted-foreground">
                Not running
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Resources</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900">
                {dashboardData?.stats.totalResources}
              </div>
              <p className="text-xs text-muted-foreground">
                Available resources
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Getting Started */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900">Getting Started</CardTitle>
            <CardDescription className="text-blue-700">
              New to Kumulus? Here's how to get started with VM deployment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <span className="text-white font-bold">1</span>
                </div>
                <h3 className="font-medium text-blue-900">Choose Image</h3>
                <p className="text-sm text-blue-700">Select Ubuntu version</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <span className="text-white font-bold">2</span>
                </div>
                <h3 className="font-medium text-blue-900">Configure Size</h3>
                <p className="text-sm text-blue-700">Pick CPU, RAM, and storage</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <span className="text-white font-bold">3</span>
                </div>
                <h3 className="font-medium text-blue-900">Deploy</h3>
                <p className="text-sm text-blue-700">Launch your VM instantly</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
