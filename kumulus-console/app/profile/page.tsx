"use client";

import { useEffect, useState } from "react";
import { DashboardLayout } from "@/components/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  Key,
  RefreshCw,
  Save,
  User,
} from "lucide-react";
import { apiClient, getErrorMessage } from "@/lib/api-client";
import { useSession } from "@/lib/auth-client";
import type { ProfileData } from "@/lib/types";

export default function ProfilePage() {
  const { data: session } = useSession();
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    image: "",
    sshPublicKey: "",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const fetchProfile = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch profile from backend API
      const response = await apiClient.getProfile();

      setProfileData(response);
      setFormData({
        name: response.profile.name,
        image: response.profile.image || "",
        sshPublicKey: response.profile.sshPublicKey || "",
      });
    } catch (err) {
      console.error("Failed to fetch profile:", err);
      setError(getErrorMessage(err));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setSuccessMessage(null);

      // Validate SSH key format (basic validation)
      if (formData.sshPublicKey && !formData.sshPublicKey.trim().startsWith("ssh-")) {
        throw new Error("SSH public key must start with 'ssh-rsa', 'ssh-ed25519', or similar");
      }

      // Update profile via backend API
      const updateData = {
        name: formData.name,
        image: formData.image || undefined,
        sshPublicKey: formData.sshPublicKey || undefined,
      };

      const response = await apiClient.updateProfile(updateData);

      setProfileData(response);
      setSuccessMessage("Profile updated successfully!");

    } catch (err) {
      console.error("Failed to save profile:", err);
      setError(getErrorMessage(err));
    } finally {
      setIsSaving(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      fetchProfile();
    }
  }, [session]);

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading profile...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error && !profileData) {
    return (
      <DashboardLayout>
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading Profile
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchProfile} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-blue-900">Profile</h1>
          <p className="text-muted-foreground">
            Manage your account settings and SSH keys
          </p>
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Profile Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Update your personal information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Your full name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  value={profileData?.profile.email || ""}
                  disabled
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground">
                  Email cannot be changed
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Email Verified</span>
                  {profileData?.profile.emailVerified ? (
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      Verified
                    </Badge>
                  ) : (
                    <Badge variant="destructive">Not Verified</Badge>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Account Created</span>
                  <span className="text-sm text-muted-foreground">
                    {profileData?.profile.createdAt
                      ? new Date(profileData.profile.createdAt).toLocaleDateString()
                      : "Unknown"}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SSH Key Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                SSH Public Key
              </CardTitle>
              <CardDescription>
                Add your SSH public key to access VMs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sshPublicKey">SSH Public Key</Label>
                <textarea
                  id="sshPublicKey"
                  value={formData.sshPublicKey}
                  onChange={(e) => setFormData({ ...formData, sshPublicKey: e.target.value })}
                  placeholder="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... <EMAIL>"
                  className="w-full min-h-[100px] px-3 py-2 text-sm border border-input rounded-md bg-background resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                />
                <p className="text-xs text-muted-foreground">
                  Paste your SSH public key here. This will be used to configure access to your VMs.
                </p>
              </div>

              {!formData.sshPublicKey && (
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-md">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                    <span className="text-sm font-medium text-amber-800">SSH Key Required</span>
                  </div>
                  <p className="text-xs text-amber-700 mt-1">
                    You need to add an SSH public key before creating VMs
                  </p>
                </div>
              )}

              {formData.sshPublicKey && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                  <div className="flex items-center gap-2">
                    <Key className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-green-800">SSH Key Configured</span>
                  </div>
                  <p className="text-xs text-green-700 mt-1">
                    Your SSH key is ready for VM access
                  </p>
                </div>
              )}

              <div className="text-xs text-muted-foreground space-y-1">
                <p><strong>How to generate an SSH key:</strong></p>
                <code className="block bg-muted p-2 rounded text-xs">
                  ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
                </code>
                <p>Then copy the content of <code>~/.ssh/id_rsa.pub</code></p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Save Button */}
        <div className="flex items-center gap-4">
          <Button 
            onClick={handleSave} 
            disabled={isSaving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSaving ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>

          {error && (
            <div className="text-sm text-red-600 bg-red-50 px-3 py-2 rounded-md">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="text-sm text-green-600 bg-green-50 px-3 py-2 rounded-md">
              {successMessage}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
