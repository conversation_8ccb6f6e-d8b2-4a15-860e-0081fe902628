"use client";

import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  RefreshCw, 
  Terminal,
  Copy,
  Loader2
} from "lucide-react";
import { apiClient } from "@/lib/api-client";

interface TunnelStatusProps {
  vmId: string;
  bastionIp?: string;
  bastionPort?: number;
  sshAccess?: string;
  initialStatus?: string;
  onStatusChange?: (status: string) => void;
}

interface TunnelHealth {
  isHealthy: boolean;
  status: 'active' | 'inactive' | 'unknown';
  lastChecked: string;
  cached?: any;
}

export function TunnelStatus({ 
  vmId, 
  bastionIp, 
  bastionPort, 
  sshAccess, 
  initialStatus = 'unknown',
  onStatusChange 
}: TunnelStatusProps) {
  const [status, setStatus] = useState<string>(initialStatus);
  const [isChecking, setIsChecking] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);

  const checkTunnelHealth = async () => {
    if (!bastionIp || !bastionPort) {
      setError("Missing bastion connection details");
      return;
    }

    setIsChecking(true);
    setError(null);

    try {
      const response = await fetch('/api/temp/validate-tunnel-health', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          bastionIp,
          port: bastionPort,
          tunnelId: vmId,
        }),
      });

      if (response.ok) {
        const data: TunnelHealth = await response.json();
        const newStatus = data.isHealthy ? 'active' : 'inactive';
        setStatus(newStatus);
        setLastChecked(new Date());
        onStatusChange?.(newStatus);
      } else {
        throw new Error(`Health check failed: ${response.status}`);
      }
    } catch (err) {
      console.error('Tunnel health check failed:', err);
      setError(err instanceof Error ? err.message : 'Health check failed');
      setStatus('unknown');
    } finally {
      setIsChecking(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const getStatusIcon = () => {
    if (isChecking) return <Loader2 className="h-4 w-4 animate-spin" />;
    
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'inactive':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'active':
        return 'SSH Available';
      case 'inactive':
        return 'SSH Unavailable';
      default:
        return 'Status Unknown';
    }
  };

  // Auto-check on mount and periodically
  useEffect(() => {
    if (bastionIp && bastionPort) {
      checkTunnelHealth();
      
      // Check every 2 minutes
      const interval = setInterval(checkTunnelHealth, 120000);
      return () => clearInterval(interval);
    }
  }, [bastionIp, bastionPort, vmId]);

  if (!bastionIp || !bastionPort) {
    return (
      <Card className="border-gray-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            SSH Access
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            SSH tunnel information not available
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-gray-200">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Terminal className="h-4 w-4" />
          SSH Access
          <Badge className={`ml-auto ${getStatusColor()}`}>
            {getStatusIcon()}
            <span className="ml-1">{getStatusText()}</span>
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Connection Details */}
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <span className="font-medium">Bastion IP:</span>
            <p className="text-muted-foreground font-mono">{bastionIp}</p>
          </div>
          <div>
            <span className="font-medium">Port:</span>
            <p className="text-muted-foreground">{bastionPort}</p>
          </div>
        </div>

        {/* SSH Command */}
        {sshAccess && (
          <div>
            <span className="text-sm font-medium">SSH Command:</span>
            <div className="flex items-center gap-2 mt-1">
              <code className={`flex-1 p-2 rounded text-xs font-mono ${
                status === 'active' 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-gray-50 border border-gray-200'
              }`}>
                {status === 'inactive' && sshAccess.startsWith('#') 
                  ? sshAccess 
                  : sshAccess
                }
              </code>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(sshAccess)}
                disabled={status === 'inactive'}
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )}

        {/* Status Information */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>
            {lastChecked 
              ? `Last checked: ${lastChecked.toLocaleTimeString()}`
              : 'Not checked yet'
            }
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={checkTunnelHealth}
            disabled={isChecking}
            className="h-6 px-2"
          >
            <RefreshCw className={`h-3 w-3 ${isChecking ? 'animate-spin' : ''}`} />
          </Button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded p-2">
            <p className="text-xs text-red-700">{error}</p>
          </div>
        )}

        {/* Status-specific Messages */}
        {status === 'inactive' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded p-2">
            <p className="text-xs text-yellow-700">
              SSH tunnel is currently unavailable. The VM may be restarting or experiencing network issues.
            </p>
          </div>
        )}

        {status === 'active' && (
          <div className="bg-green-50 border border-green-200 rounded p-2">
            <p className="text-xs text-green-700">
              SSH tunnel is active and ready for connections.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
