"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  CheckCircle,
  Copy,
  Cpu,
  HardDrive,
  MemoryStick,
  Monitor,
  Play,
  Square,
  Terminal,
  Trash2,
  XCircle,
  AlertCircle,
  Clock,
} from "lucide-react";
import { apiClient, getErrorMessage } from "@/lib/api-client";

interface VM {
  id: string;
  name: string;
  status: "running" | "stopped" | "creating" | "starting" | "stopping" | "error";
  image: string;
  size: {
    vcpus: number;
    memory: number;
    disk: number;
  };
  ipAddress?: string;
  region: string;
  createdAt: string;
  cost: {
    hourly: number;
    monthly: number;
  };
  sshAccess?: string;
  bastionIp?: string;
  bastionPort?: number;
}

interface VMDetailsModalProps {
  vm: VM | null;
  isOpen: boolean;
  onClose: () => void;
  onVMUpdate: () => void;
}

export function VMDetailsModal({ vm, isOpen, onClose, onVMUpdate }: VMDetailsModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [error, setError] = useState<string>("");

  if (!vm) return null;

  const formatMemory = (memoryGB: number): string => {
    if (memoryGB >= 1) {
      return `${memoryGB} GB`;
    } else {
      const memoryMB = Math.round(memoryGB * 1024);
      return `${memoryMB} MB`;
    }
  };

  const getStatusColor = (status: VM["status"]) => {
    switch (status) {
      case "running":
        return "bg-green-100 text-green-800 border-green-200";
      case "stopped":
        return "bg-gray-100 text-gray-800 border-gray-200";
      case "creating":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "starting":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "stopping":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "error":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: VM["status"]) => {
    switch (status) {
      case "running":
        return <CheckCircle className="h-4 w-4" />;
      case "stopped":
        return <Square className="h-4 w-4" />;
      case "creating":
        return <Clock className="h-4 w-4" />;
      case "starting":
        return <Clock className="h-4 w-4" />;
      case "stopping":
        return <Clock className="h-4 w-4" />;
      case "error":
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const copySSHCommand = async () => {
    if (!vm.sshAccess) return;
    
    try {
      await navigator.clipboard.writeText(vm.sshAccess);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy SSH command:", err);
    }
  };

  const handleVMAction = async (action: "start" | "stop" | "delete") => {
    setIsLoading(true);
    setError("");

    try {
      let response;
      switch (action) {
        case "start":
          response = await apiClient.startVM(vm.id);
          break;
        case "stop":
          response = await apiClient.stopVM(vm.id);
          break;
        case "delete":
          response = await apiClient.deleteVM(vm.id);
          break;
      }

      console.log(`VM ${action} successful:`, response);
      onVMUpdate(); // Refresh the VM list
      
      if (action === "delete") {
        onClose(); // Close modal after deletion
      }
    } catch (err) {
      console.error(`Failed to ${action} VM:`, err);
      setError(getErrorMessage(err));
    } finally {
      setIsLoading(false);
    }
  };

  // Generate SSH command if we have bastion info
  const sshCommand = vm.sshTunnel?.sshAccess
    ? vm.sshTunnel.sshAccess
    : vm.bastionIp && vm.bastionPort
    ? `ssh ubuntu@${vm.bastionIp} -p ${vm.bastionPort}`
    : vm.sshAccess || "SSH information not available";

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            {vm.name}
          </DialogTitle>
          <DialogDescription>
            Virtual machine details and management
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Basic Info */}
          <div className="flex items-center justify-between">
            <Badge className={getStatusColor(vm.status)}>
              {getStatusIcon(vm.status)}
              <span className="ml-1 capitalize">{vm.status}</span>
            </Badge>
            <div className="text-sm text-muted-foreground">
              Created: {new Date(vm.createdAt).toLocaleDateString()}
            </div>
          </div>

          {/* VM Specifications */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Specifications</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <Cpu className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{vm.size.vcpus} vCPU{vm.size.vcpus !== 1 ? "s" : ""}</div>
                    <div className="text-xs text-muted-foreground">Processor</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <MemoryStick className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{formatMemory(vm.size.memory)}</div>
                    <div className="text-xs text-muted-foreground">Memory</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <HardDrive className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{vm.size.disk} GB</div>
                    <div className="text-xs text-muted-foreground">Storage</div>
                  </div>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t">
                <div className="flex items-center justify-between text-sm">
                  <span>Operating System:</span>
                  <span className="font-medium">{vm.image}</span>
                </div>
                <div className="flex items-center justify-between text-sm mt-2">
                  <span>Region:</span>
                  <span className="font-medium">{vm.region}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SSH Access */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Terminal className="h-4 w-4" />
                SSH Access
              </CardTitle>
              <CardDescription>
                Connect to your VM using SSH
              </CardDescription>
            </CardHeader>
            <CardContent>
              {vm.status === "running" && (vm.sshTunnel?.sshAccess || vm.bastionIp || vm.sshAccess) ? (
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium">SSH Command:</label>
                    <div className="flex items-center gap-2 mt-1">
                      <code className="flex-1 p-2 bg-gray-50 border rounded text-sm font-mono">
                        {sshCommand}
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={copySSHCommand}
                        disabled={!sshCommand || sshCommand === "SSH information not available"}
                      >
                        {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                  {(vm.sshTunnel?.bastionIp || vm.bastionIp) && (vm.sshTunnel?.bastionPort || vm.bastionPort) && (
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Bastion IP:</span>
                        <p className="text-muted-foreground font-mono">{vm.sshTunnel?.bastionIp || vm.bastionIp}</p>
                      </div>
                      <div>
                        <span className="font-medium">SSH Port:</span>
                        <p className="text-muted-foreground">{vm.sshTunnel?.bastionPort || vm.bastionPort}</p>
                      </div>
                    </div>
                  )}
                  <div className="bg-green-50 border border-green-200 rounded p-3">
                    <p className="text-sm text-green-700">
                      ✅ SSH access is available. Use the command above to connect to your VM.
                    </p>
                  </div>
                </div>
              ) : vm.status === "running" ? (
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  <p className="text-sm text-blue-700">
                    🔄 Setting up SSH tunnel... This may take a few moments.
                  </p>
                </div>
              ) : (
                <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
                  <p className="text-sm text-yellow-700">
                    ⚠️ SSH access is only available when the VM is running.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
              
          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between">
            <div className="flex gap-2">
              {vm.status === "stopped" && (
                <Button
                  onClick={() => handleVMAction("start")}
                  disabled={isLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Starting...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Start VM
                    </>
                  )}
                </Button>
              )}
              {vm.status === "running" && (
                <Button
                  onClick={() => handleVMAction("stop")}
                  disabled={isLoading}
                  variant="outline"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                      Stopping...
                    </>
                  ) : (
                    <>
                      <Square className="h-4 w-4 mr-2" />
                      Stop VM
                    </>
                  )}
                </Button>
              )}
              {(vm.status === "starting" || vm.status === "stopping") && (
                <Button disabled variant="outline">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                  {vm.status === "starting" ? "Starting..." : "Stopping..."}
                </Button>
              )}
              {vm.status === "creating" && (
                <Button disabled variant="outline">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  Creating VM...
                </Button>
              )}
            </div>
            <Button
              onClick={() => handleVMAction("delete")}
              disabled={isLoading || vm.status === "creating"}
              variant="destructive"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete VM
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
