name: Build & Release Kumulus Flare Agent

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        os: [linux]
        include:
          - os: linux
            target: x86_64-unknown-linux-gnu
            ext: ""

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install Deno v2.4.2
        uses: denoland/setup-deno@v2.0.2
        with:
          deno-version: 2.4.2

      - name: Compile binary
        run: |
          deno compile \
            --env -A --no-check \
            --target=${{ matrix.target }} \
            --output kumulus-agent${{ matrix.ext }} \
            kumulus-flare-agent/src/main.ts

      - name: Publish binary to public repo
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ github.ref_name }}
          repository: kollectyve-labs/kumulus-tools
          token: ${{ secrets.KUMULUS_TOOLS_TOKEN }}
          files: |
            kumulus-agent${{ matrix.ext }}
