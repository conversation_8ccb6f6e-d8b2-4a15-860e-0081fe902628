import { DB } from "https://deno.land/x/sqlite@v3.8.0/mod.ts";

export interface TunnelState {
  tunnel_id: string;
  bastion_id: string;
  bastion_ip: string;
  assigned_port: number;
  status: string;
  last_connected?: string;
  retry_count: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface AgentConfig {
  key: string;
  value: string;
  updated_at: string;
}

export interface ConnectionHistory {
  id: number;
  tunnel_id: string;
  connected_at: string;
  disconnected_at?: string;
  duration_ms?: number;
  disconnect_reason?: string;
  error_message?: string;
}

export class AgentDatabase {
  private db: DB;
  private dbPath: string;

  constructor(dbPath: string = "./agent-state.db") {
    this.dbPath = dbPath;
    this.db = new DB(dbPath);
    this.initializeDatabase();
  }

  private initializeDatabase(): void {
    // Create tunnel_state table
    this.db.execute(`
      CREATE TABLE IF NOT EXISTS tunnel_state (
        tunnel_id TEXT PRIMARY KEY,
        bastion_id TEXT NOT NULL,
        bastion_ip TEXT NOT NULL,
        assigned_port INTEGER NOT NULL,
        status TEXT NOT NULL DEFAULT 'assigned',
        last_connected TEXT,
        retry_count INTEGER DEFAULT 0,
        error_message TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create agent_config table
    this.db.execute(`
      CREATE TABLE IF NOT EXISTS agent_config (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create connection_history table
    this.db.execute(`
      CREATE TABLE IF NOT EXISTS connection_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tunnel_id TEXT NOT NULL,
        connected_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        disconnected_at TEXT,
        duration_ms INTEGER,
        disconnect_reason TEXT,
        error_message TEXT,
        FOREIGN KEY (tunnel_id) REFERENCES tunnel_state(tunnel_id)
      )
    `);

    // Create indexes for better performance
    this.db.execute(`CREATE INDEX IF NOT EXISTS idx_tunnel_status ON tunnel_state(status)`);
    this.db.execute(`CREATE INDEX IF NOT EXISTS idx_tunnel_bastion ON tunnel_state(bastion_id)`);
    this.db.execute(`CREATE INDEX IF NOT EXISTS idx_history_tunnel ON connection_history(tunnel_id)`);

    console.log("✅ Agent database initialized");
  }

  // Tunnel State Management
  saveTunnelState(tunnel: TunnelState): void {
    this.db.query(`
      INSERT OR REPLACE INTO tunnel_state 
      (tunnel_id, bastion_id, bastion_ip, assigned_port, status, last_connected, retry_count, error_message, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      tunnel.tunnel_id,
      tunnel.bastion_id,
      tunnel.bastion_ip,
      tunnel.assigned_port,
      tunnel.status,
      tunnel.last_connected || null,
      tunnel.retry_count,
      tunnel.error_message || null,
      tunnel.created_at,
      new Date().toISOString(),
    ]);
  }

  getTunnelState(tunnelId: string): TunnelState | null {
    const result = this.db.query(`
      SELECT * FROM tunnel_state WHERE tunnel_id = ?
    `, [tunnelId]);

    if (result.length === 0) return null;

    const row = result[0];
    return {
      tunnel_id: row[0] as string,
      bastion_id: row[1] as string,
      bastion_ip: row[2] as string,
      assigned_port: row[3] as number,
      status: row[4] as string,
      last_connected: row[5] as string || undefined,
      retry_count: row[6] as number,
      error_message: row[7] as string || undefined,
      created_at: row[8] as string,
      updated_at: row[9] as string,
    };
  }

  getAllTunnelStates(): TunnelState[] {
    const results = this.db.query(`SELECT * FROM tunnel_state ORDER BY created_at DESC`);
    
    return results.map(row => ({
      tunnel_id: row[0] as string,
      bastion_id: row[1] as string,
      bastion_ip: row[2] as string,
      assigned_port: row[3] as number,
      status: row[4] as string,
      last_connected: row[5] as string || undefined,
      retry_count: row[6] as number,
      error_message: row[7] as string || undefined,
      created_at: row[8] as string,
      updated_at: row[9] as string,
    }));
  }

  updateTunnelStatus(tunnelId: string, status: string, errorMessage?: string): void {
    const updates: string[] = ["status = ?", "updated_at = ?"];
    const values: any[] = [status, new Date().toISOString()];

    if (status === "connected") {
      updates.push("last_connected = ?");
      values.push(new Date().toISOString());
    }

    if (errorMessage !== undefined) {
      updates.push("error_message = ?");
      values.push(errorMessage);
    }

    values.push(tunnelId);

    this.db.query(`
      UPDATE tunnel_state 
      SET ${updates.join(", ")}
      WHERE tunnel_id = ?
    `, values);
  }

  incrementRetryCount(tunnelId: string): void {
    this.db.query(`
      UPDATE tunnel_state 
      SET retry_count = retry_count + 1, updated_at = ?
      WHERE tunnel_id = ?
    `, [new Date().toISOString(), tunnelId]);
  }

  deleteTunnelState(tunnelId: string): void {
    this.db.query(`DELETE FROM tunnel_state WHERE tunnel_id = ?`, [tunnelId]);
  }

  // Agent Configuration Management
  setConfig(key: string, value: string): void {
    this.db.query(`
      INSERT OR REPLACE INTO agent_config (key, value, updated_at)
      VALUES (?, ?, ?)
    `, [key, value, new Date().toISOString()]);
  }

  getConfig(key: string): string | null {
    const result = this.db.query(`SELECT value FROM agent_config WHERE key = ?`, [key]);
    return result.length > 0 ? result[0][0] as string : null;
  }

  getAllConfig(): Record<string, string> {
    const results = this.db.query(`SELECT key, value FROM agent_config`);
    const config: Record<string, string> = {};
    
    for (const row of results) {
      config[row[0] as string] = row[1] as string;
    }
    
    return config;
  }

  // Connection History Management
  recordConnection(tunnelId: string): number {
    const result = this.db.query(`
      INSERT INTO connection_history (tunnel_id, connected_at)
      VALUES (?, ?)
    `, [tunnelId, new Date().toISOString()]);
    
    return result.lastInsertRowId as number;
  }

  recordDisconnection(
    connectionId: number, 
    reason?: string, 
    errorMessage?: string
  ): void {
    const disconnectedAt = new Date().toISOString();
    
    // Calculate duration
    const connectionResult = this.db.query(`
      SELECT connected_at FROM connection_history WHERE id = ?
    `, [connectionId]);
    
    let durationMs: number | null = null;
    if (connectionResult.length > 0) {
      const connectedAt = new Date(connectionResult[0][0] as string);
      durationMs = Date.now() - connectedAt.getTime();
    }

    this.db.query(`
      UPDATE connection_history 
      SET disconnected_at = ?, duration_ms = ?, disconnect_reason = ?, error_message = ?
      WHERE id = ?
    `, [disconnectedAt, durationMs, reason || null, errorMessage || null, connectionId]);
  }

  getConnectionHistory(tunnelId: string, limit: number = 10): ConnectionHistory[] {
    const results = this.db.query(`
      SELECT * FROM connection_history 
      WHERE tunnel_id = ? 
      ORDER BY connected_at DESC 
      LIMIT ?
    `, [tunnelId, limit]);

    return results.map(row => ({
      id: row[0] as number,
      tunnel_id: row[1] as string,
      connected_at: row[2] as string,
      disconnected_at: row[3] as string || undefined,
      duration_ms: row[4] as number || undefined,
      disconnect_reason: row[5] as string || undefined,
      error_message: row[6] as string || undefined,
    }));
  }

  // Cleanup old history records
  cleanupHistory(daysToKeep: number = 30): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    this.db.query(`
      DELETE FROM connection_history 
      WHERE connected_at < ?
    `, [cutoffDate.toISOString()]);
  }

  // Database maintenance
  vacuum(): void {
    this.db.execute("VACUUM");
  }

  close(): void {
    this.db.close();
  }

  // Get database stats
  getStats(): any {
    const tunnelCount = this.db.query(`SELECT COUNT(*) FROM tunnel_state`)[0][0];
    const configCount = this.db.query(`SELECT COUNT(*) FROM agent_config`)[0][0];
    const historyCount = this.db.query(`SELECT COUNT(*) FROM connection_history`)[0][0];
    
    return {
      tunnels: tunnelCount,
      configs: configCount,
      history: historyCount,
      dbPath: this.dbPath,
    };
  }
}

// Global database instance
export const agentDB = new AgentDatabase();
