import { Hono } from "@hono/hono";
//import { cors } from '@hono/hono/cors'
//app.use(cors({ origin: 'http://localhost:8000' }));

export const app = new Hono();

const runningTunnels: Record<string, any> = {};

// Smart tunnel monitoring and recovery
let tunnelMonitoringInterval: ReturnType<typeof setInterval> | null = null;
const ENABLE_TUNNEL_MONITORING = Deno.env.get("ENABLE_TUNNEL_MONITORING") === "true";

async function startTunnelMonitoring() {
  if (!ENABLE_TUNNEL_MONITORING) {
    console.log(`[LOG] Tunnel monitoring disabled via environment variable`);
    return;
  }

  if (tunnelMonitoringInterval) return; // Already running

  console.log(`[LOG] Starting smart tunnel monitoring...`);

  tunnelMonitoringInterval = setInterval(async () => {
    try {
      await checkAndRecoverTunnels();
    } catch (error) {
      console.error(`[ERROR] Tunnel monitoring error:`, error);
    }
  }, 120000); // Check every 2 minutes (reduced frequency)
}

async function checkAndRecoverTunnels() {
  const backendUrl = Deno.env.get("KUMULUS_BACKEND_URL") || "http://localhost:8000";

  for (const [vmId, tunnelInfo] of Object.entries(runningTunnels)) {
    if (!tunnelInfo || typeof tunnelInfo === 'string') continue;

    try {
      // Check if tunnel process is still running
      const isProcessAlive = tunnelInfo.process && !tunnelInfo.process.status;

      if (!isProcessAlive) {
        // Circuit breaker: prevent infinite recreation attempts
        const now = Date.now();
        const timeSinceCreation = now - (tunnelInfo.createdAt || 0);
        const failureCount = tunnelInfo.failureCount || 0;

        // Don't attempt recovery if:
        // 1. Too many failures (more than 3)
        // 2. Recent failures (less than 5 minutes since last attempt)
        if (failureCount >= 3) {
          console.log(`[ERROR] Tunnel for VM ${vmId} has failed ${failureCount} times, stopping recovery attempts`);
          continue;
        }

        if (tunnelInfo.lastRecoveryAttempt && (now - tunnelInfo.lastRecoveryAttempt) < 300000) { // 5 minutes
          console.log(`[WARN] Skipping tunnel recovery for VM ${vmId} - too recent (${Math.round((now - tunnelInfo.lastRecoveryAttempt) / 1000)}s ago)`);
          continue;
        }

        console.log(`[WARN] Tunnel process died for VM ${vmId}, attempting recovery (attempt ${failureCount + 1}/3)...`);

        // Update failure tracking
        runningTunnels[vmId] = {
          ...tunnelInfo,
          failureCount: failureCount + 1,
          lastRecoveryAttempt: now,
        };

        // Notify backend about tunnel failure
        try {
          await fetch(`${backendUrl}/api/temp/validate-tunnel-health`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              bastionIp: tunnelInfo.bastionIp,
              port: tunnelInfo.bastionPort,
              tunnelId: vmId,
            }),
          });
        } catch (notifyError) {
          console.warn(`[WARN] Failed to notify backend of tunnel failure:`, notifyError.message);
        }

        // Attempt to restart tunnel with exponential backoff
        try {
          await recreateTunnel(vmId, tunnelInfo);
          // Reset failure count on successful recreation
          runningTunnels[vmId].failureCount = 0;
        } catch (recoveryError) {
          console.error(`[ERROR] Failed to recover tunnel for VM ${vmId}:`, recoveryError.message);
        }
      }
    } catch (error) {
      console.error(`[ERROR] Error checking tunnel for VM ${vmId}:`, error);
    }
  }
}

async function recreateTunnel(vmId: string, originalTunnelInfo: any) {
  console.log(`[LOG] Recreating tunnel for VM ${vmId}...`);

  const backendUrl = Deno.env.get("KUMULUS_BACKEND_URL") || "http://localhost:8000";

  try {
    // Clean up any existing process first
    if (originalTunnelInfo.process) {
      try {
        originalTunnelInfo.process.kill("SIGTERM");
      } catch (killError) {
        console.warn(`[WARN] Failed to kill existing tunnel process:`, killError.message);
      }
    }

    // Request new tunnel from backend
    const tunnelRes = await fetch(`${backendUrl}/api/temp/request-vm-ssh-tunnel`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        vmContainerId: vmId,
        sshUsername: "ubuntu", // Default username
      }),
    });

    const tunnelInfo = await tunnelRes.json();

    if (!tunnelRes.ok || !tunnelInfo.success) {
      throw new Error(tunnelInfo.error || "Failed to request new tunnel");
    }

    // Test SSH connectivity before creating tunnel
    console.log(`[LOG] Testing SSH connectivity to ${tunnelInfo.bastionIp}...`);
    const testSshCmd = [
      "ssh",
      "-o", "StrictHostKeyChecking=no",
      "-o", "UserKnownHostsFile=/dev/null",
      "-o", "ConnectTimeout=10",
      "-o", "BatchMode=yes",
      `ubuntu@${tunnelInfo.bastionIp}`,
      "echo 'SSH test successful'"
    ];

    const testProcess = new Deno.Command(testSshCmd[0], {
      args: testSshCmd.slice(1),
      stdout: "piped",
      stderr: "piped",
    });

    const testResult = await testProcess.output();

    if (testResult.code !== 0) {
      const stderr = new TextDecoder().decode(testResult.stderr);
      throw new Error(`SSH connectivity test failed: ${stderr}`);
    }

    console.log(`[LOG] SSH connectivity test passed`);

    // Start new SSH tunnel process
    const sshTunnelCmd = [
      "ssh",
      "-o", "StrictHostKeyChecking=no",
      "-o", "UserKnownHostsFile=/dev/null",
      "-o", "LogLevel=ERROR",
      "-o", "ServerAliveInterval=30",
      "-o", "ServerAliveCountMax=3",
      "-o", "ExitOnForwardFailure=yes",
      "-N",
      "-R", `${tunnelInfo.assignedPort}:localhost:${originalTunnelInfo.vmSshPort || 2222}`,
      `ubuntu@${tunnelInfo.bastionIp}`,
    ];

    console.log(`[LOG] Starting SSH tunnel: ${sshTunnelCmd.join(" ")}`);

    const tunnelProcess = new Deno.Command(sshTunnelCmd[0], {
      args: sshTunnelCmd.slice(1),
      stdout: "piped",
      stderr: "piped",
    });

    const child = tunnelProcess.spawn();

    // Update tunnel tracking with better metadata
    runningTunnels[vmId] = {
      ...originalTunnelInfo,
      process: child,
      command: sshTunnelCmd.join(" "),
      bastionPort: tunnelInfo.assignedPort,
      bastionIp: tunnelInfo.bastionIp,
      vmSshPort: originalTunnelInfo.vmSshPort || 2222,
      createdAt: Date.now(),
      lastRecoveryAttempt: Date.now(),
    };

    // Give tunnel a moment to establish
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check if tunnel process is still alive
    if (child.status) {
      const stderr = new TextDecoder().decode(await child.stderr.getReader().read().then(r => r.value || new Uint8Array()));
      throw new Error(`SSH tunnel process died immediately: ${stderr}`);
    }

    // Notify backend that tunnel is active
    await fetch(`${backendUrl}/api/temp/activate-vm-ssh-tunnel`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ vmContainerId: vmId }),
    });

    console.log(`[LOG] Successfully recreated tunnel for VM ${vmId} on port ${tunnelInfo.assignedPort}`);

  } catch (error) {
    console.error(`[ERROR] Failed to recreate tunnel for VM ${vmId}:`, error.message);
    throw error;
  }
}

interface VMRequest {
  username: string;
  sshKey: string;
  cpu: number;
  memory: string;
  disk: string;
}

interface VMControlRequest {
  vmId: string;
}

async function runCommand(
  cmd: string,
  args: string[] = [],
): Promise<{ code: number; stdout: string; stderr: string }> {
  try {
    console.log(`[LOG] Running command: ${cmd} ${args.join(" ")}`);

    const command = new Deno.Command(cmd, {
      args,
      stdout: "piped",
      stderr: "piped",
    });

    const { success, stdout, stderr } = await command.output();

    return {
      code: success ? 0 : 1,
      stdout: new TextDecoder().decode(stdout),
      stderr: new TextDecoder().decode(stderr),
    };
  } catch (error) {
    console.error(`[ERROR] Command execution failed: ${error.message}`);
    return {
      code: 1,
      stdout: "",
      stderr: error.message,
    };
  }
}

// Port ranges for different applications
const PORT_RANGES = {
  odoo: { start: 8069, end: 8169 },
  wordpress: { start: 8080, end: 8180 },
  libreoffice: { start: 9980, end: 10080 },
  nextcloud: { start: 7000, end: 7100 },
} as const;

async function findFreePort(
  startPort: number,
  endPort: number,
): Promise<number> {
  for (let port = startPort; port <= endPort; port++) {
    try {
      const listener = await Deno.listen({ port });
      listener.close();
      return port;
    } catch (_) {
      continue;
    }
  }
  throw new Error(`No available ports in range ${startPort}-${endPort}`);
}

function generateDockerfile(username: string, sshKey: string): string {
  console.log(`[LOG] Generating Dockerfile for user: ${username}`);

  try {
    const encodedSshKey = btoa(sshKey);
    console.log(`[LOG] SSH Key encoded successfully`);

    return `
      FROM ubuntu:22.04

      # Configure apt for faster downloads and better reliability
      ENV DEBIAN_FRONTEND=noninteractive
      ENV APT_KEY_DONT_WARN_ON_DANGEROUS_USAGE=1
      
      # Configure apt to retry and use multiple mirrors
      RUN echo 'Acquire::Retries "3";' > /etc/apt/apt.conf.d/80-retries && \
          echo 'Acquire::http::Pipeline-Depth "5";' >> /etc/apt/apt.conf.d/80-retries && \
          echo 'Acquire::http::Timeout "30";' >> /etc/apt/apt.conf.d/80-retries && \
          echo 'APT::Install-Recommends "false";' >> /etc/apt/apt.conf.d/80-retries && \
          echo "deb mirror://mirrors.ubuntu.com/mirrors.txt jammy main restricted universe multiverse" > /etc/apt/sources.list && \
          echo "deb mirror://mirrors.ubuntu.com/mirrors.txt jammy-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
          echo "deb mirror://mirrors.ubuntu.com/mirrors.txt jammy-security main restricted universe multiverse" >> /etc/apt/sources.list
      
      # Install necessary packages with retries
      RUN apt-get update -y && \
          apt-get install -y --no-install-recommends openssh-server sudo ca-certificates && \
          apt-get clean && \
          rm -rf /var/lib/apt/lists/*
      
      # Ensure privilege separation directory exists for SSH
      RUN mkdir -p /run/sshd && chmod 0755 /run/sshd
      
      # Create a new user with sudo privileges
      RUN useradd -m -s /bin/bash ${username} && \
          usermod -aG sudo ${username} && \
          mkdir -p /home/<USER>/.ssh
      
      # Set up SSH key for the user
      RUN echo "${encodedSshKey}" | base64 -d > /home/<USER>/.ssh/authorized_keys && \
          chmod 700 /home/<USER>/.ssh && \
          chmod 600 /home/<USER>/.ssh/authorized_keys && \
          chown -R ${username}:${username} /home/<USER>/.ssh
      
      # Configure SSH server
      RUN sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config && \
          sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config && \
          sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config && \
          echo "AllowUsers ${username}" >> /etc/ssh/sshd_config
      
      # Expose SSH port
      EXPOSE 22
      
      # Start SSH service
      CMD ["/usr/sbin/sshd", "-D"]
      `;
  } catch (error) {
    console.error(`[ERROR] Failed to generate Dockerfile: ${error.message}`);
    throw error;
  }
}

interface AppRequest {
  type: "odoo" | "wordpress" | "libreoffice" | "nextcloud";
  cpu: number;
  memory: string;
  deploymentId: string;
}

app.get("/", (c) => c.text("Provision Agent Running !"));

app.post("/create-vm", async (c) => {
  try {
    const body: VMRequest = await c.req.json();
    console.log(
      `[LOG] Received VM creation request for user: ${body.username}`,
    );

    // Validate all required parameters
    const requiredParams: (keyof VMRequest)[] = [
      "username",
      "sshKey",
      "cpu",
      "memory",
      "disk",
    ];
    for (const param of requiredParams) {
      if (!body[param]) {
        console.error(`[ERROR] Missing required parameter: ${param}`);
        return c.json({ error: `Missing required parameter: ${param}` }, 400);
      }
    }

    const vmId = crypto.randomUUID();
    const dockerfilePath = `/tmp/Dockerfile-${vmId}`;
    const imageName = `ubuntu-vm-${vmId}`;

    console.log(`[LOG] Generated VM ID: ${vmId}`);
    console.log(`[LOG] Dockerfile path: ${dockerfilePath}`);
    console.log(`[LOG] Image name: ${imageName}`);

    const dockerfileContent = generateDockerfile(body.username, body.sshKey);
    await Deno.writeTextFile(dockerfilePath, dockerfileContent);
    console.log(`[LOG] Dockerfile written successfully`);

    const sshPort = await findFreePort(2222, 2322);
    console.log(`[LOG] Selected SSH port: ${sshPort}`);

    const buildResult = await runCommand("docker", [
      "build",
      "--no-cache",
      "--progress=plain",
      "--network=host",
      "-t",
      imageName,
      "-f",
      dockerfilePath,
      ".",
    ]);

    console.log(`[LOG] Build command exit code: ${buildResult.code}`);

    if (buildResult.code !== 0) {
      console.error(`[ERROR] Docker build failed`);
      console.error(`[ERROR] Build stderr: ${buildResult.stderr}`);
      console.error(`[ERROR] Build stdout: ${buildResult.stdout}`);

      return c.json({
        error: "Failed to build Docker image",
        details: {
          stderr: buildResult.stderr,
          stdout: buildResult.stdout,
        },
      }, 500);
    }

    //TODO; remove the hardcoded DNS
    const runResult = await runCommand("docker", [
      "run",
      "-d",
      "--dns",
      "*******",
      "--dns",
      "*******",
      `--cpus=${body.cpu}`,
      `--memory=${body.memory}`,
      `-p`,
      `${sshPort}:22`,
      `--name`,
      vmId,
      imageName,
    ]);

    console.log(`[LOG] Run command exit code: ${runResult.code}`);

    if (runResult.code !== 0) {
      console.error(`[ERROR] Docker run failed`);
      console.error(`[ERROR] Run stderr: ${runResult.stderr}`);
      console.error(`[ERROR] Run stdout: ${runResult.stdout}`);

      return c.json({
        error: "Failed to start Docker container",
        details: {
          stderr: runResult.stderr,
          stdout: runResult.stdout,
        },
      }, 500);
    }

    await Deno.remove(dockerfilePath);
    console.log(`[LOG] Temporary Dockerfile removed`);

    // --- Automatic SSH Tunnel Setup ---
    console.log(`[LOG] Setting up SSH tunnel for VM access...`);

    const backendUrl = Deno.env.get("KUMULUS_BACKEND_URL") || "http://localhost:8000";
    let tunnelInfo = null;

    try {
      // Request VM SSH tunnel from backend
      const tunnelRes = await fetch(`${backendUrl}/api/temp/request-vm-ssh-tunnel`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          vmContainerId: vmId,
          sshUsername: body.username,
        }),
      });

      tunnelInfo = await tunnelRes.json();

      if (!tunnelRes.ok || !tunnelInfo.success) {
        throw new Error(
          tunnelInfo.error || "Failed to request VM SSH tunnel"
        );
      }

      console.log(`[LOG] VM SSH tunnel assigned:`, {
        bastionIp: tunnelInfo.bastionIp,
        assignedPort: tunnelInfo.assignedPort,
        vmId: vmId
      });

    } catch (err) {
      const errMsg = err instanceof Error ? err.message : String(err);
      console.error(`[ERROR] VM SSH tunnel request failed:`, errMsg);

      // Don't fail VM creation if tunnel setup fails - return VM without tunnel
      console.warn(`[WARN] Continuing without SSH tunnel setup`);
      return c.json({
        vmId,
        sshPort,
        username: body.username,
        status: "Running",
        warning: "VM created successfully but SSH tunnel setup failed",
        error: errMsg,
      }, 201);
    }

    // Start SSH tunnel process
    const sshTunnelCmd = [
      "ssh",
      "-o", "StrictHostKeyChecking=no",
      "-o", "UserKnownHostsFile=/dev/null",
      "-o", "LogLevel=ERROR",
      "-N",
      "-R", `${tunnelInfo.assignedPort}:localhost:${sshPort}`,
      `${body.username}@${tunnelInfo.bastionIp}`,
    ];

    try {
      console.log(`[LOG] Starting SSH tunnel:`, sshTunnelCmd.join(" "));

      // Start tunnel in background
      const tunnelProcess = new Deno.Command(sshTunnelCmd[0], {
        args: sshTunnelCmd.slice(1),
        stdout: "piped",
        stderr: "piped",
      });

      const child = tunnelProcess.spawn();

      // Track the tunnel process for cleanup and monitoring
      runningTunnels[vmId] = {
        process: child,
        command: sshTunnelCmd.join(" "),
        bastionPort: tunnelInfo.assignedPort,
        bastionIp: tunnelInfo.bastionIp,
        vmSshPort: sshPort,
        createdAt: Date.now(),
      };

      // Start tunnel monitoring if not already running
      await startTunnelMonitoring();

      // Give tunnel a moment to establish
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Notify backend that tunnel is active
      try {
        await fetch(`${backendUrl}/api/temp/activate-vm-ssh-tunnel`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ vmContainerId: vmId }),
        });
        console.log(`[LOG] VM SSH tunnel activated in backend`);
      } catch (activateErr) {
        console.warn(`[WARN] Failed to activate tunnel in backend:`, activateErr.message);
      }

      console.log(`[LOG] SSH tunnel established successfully`);

      // Update VM status to "running" in backend
      try {
        await fetch(`${backendUrl}/api/vms/update-status`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            vmId: vmId,
            status: "running",
            message: "VM created and SSH tunnel established successfully"
          }),
        });
        console.log(`[LOG] VM status updated to 'running' in backend`);
      } catch (statusErr) {
        console.warn(`[WARN] Failed to update VM status in backend:`, statusErr.message);
      }

    } catch (err) {
      console.error(`[ERROR] Failed to start SSH tunnel process:`, err);

      // Don't fail VM creation - return VM with tunnel info but warning
      return c.json({
        vmId,
        sshPort,
        username: body.username,
        status: "Running",
        bastionIp: tunnelInfo.bastionIp,
        bastionPort: tunnelInfo.assignedPort,
        sshAccess: tunnelInfo.sshAccess,
        warning: "VM created but SSH tunnel process failed to start",
        error: err instanceof Error ? err.message : String(err),
      }, 201);
    }

    // Return success with SSH access information
    return c.json({
      vmId,
      sshPort,
      username: body.username,
      status: "Running",
      bastionIp: tunnelInfo.bastionIp,
      bastionPort: tunnelInfo.assignedPort,
      sshAccess: tunnelInfo.sshAccess,
      tunnelStatus: "active",
    }, 201);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : String(error);
    console.error(`[FATAL ERROR] VM creation completely failed: ${errMsg}`);
    if (error instanceof Error && error.stack) {
      console.error(`[FATAL ERROR] Error stack: ${error.stack}`);
    }
    return c.json({
      error: "VM creation failed",
      details: errMsg,
    }, 500);
  }
});

// Helper function to check if container exists
async function containerExists(containerId: string): Promise<boolean> {
  const inspectResult = await runCommand("docker", ["inspect", containerId]);
  return inspectResult.code === 0;
}

// Stop VM
app.post("/stop-vm", async (c) => {
  try {
    const body: VMControlRequest = await c.req.json();
    console.log(`[LOG] Received VM stop request for : ${body.vmId}`);

    // Check if container exists
    const exists = await containerExists(body.vmId);
    if (!exists) {
      return c.json({
        error: "VM not found",
        details: `Container ${body.vmId} does not exist`,
      }, 404);
    }

    const stopResult = await runCommand("docker", ["stop", body.vmId]);

    if (stopResult.code !== 0) {
      console.error(`[ERROR] Docker stop failed`);
      console.error(`[ERROR] Stop stderr: ${stopResult.stderr}`);
      console.error(`[ERROR] Stop stdout: ${stopResult.stdout}`);
      return c.json({
        error: "Failed to stop VM",
        details: stopResult.stderr,
      }, 500);
    }

    return c.json({
      message: "VM stopped successfully",
    }, 200);
  } catch (error) {
    console.error(`[ERROR] VM stop failed: ${error.message}`);
    return c.json({ error: "VM stop failed", details: error.message }, 500);
  }
});

// Start VM
app.post("/start-vm", async (c) => {
  try {
    const body: VMControlRequest = await c.req.json();
    console.log(`[LOG] Received VM start request for : ${body.vmId}`);

    // Check if container exists
    const exists = await containerExists(body.vmId);
    if (!exists) {
      return c.json({
        error: "VM not found",
        details: `Container ${body.vmId} does not exist`,
      }, 404);
    }

    const startResult = await runCommand("docker", ["start", body.vmId]);

    if (startResult.code !== 0) {
      console.error(`[ERROR] Docker start failed`);
      console.error(`[ERROR] Start stderr: ${startResult.stderr}`);
      console.error(`[ERROR] Start stdout: ${startResult.stdout}`);
      return c.json({
        error: "Failed to start VM",
        details: startResult.stderr,
      }, 500);
    }

    return c.json({
      message: "VM started successfully",
    }, 200);
  } catch (error) {
    console.error(`[ERROR] VM start failed: ${error.message}`);
    return c.json({ error: "VM start failed", details: error.message }, 500);
  }
});

// Delete VM
app.post("/delete-vm", async (c) => {
  try {
    const body: VMControlRequest = await c.req.json();
    console.log(`[LOG] Received VM delete request for : ${body.vmId}`);

    // Kill SSH tunnel if running
    if (runningTunnels[body.vmId]) {
      try {
        const tunnelInfo = runningTunnels[body.vmId];

        if (tunnelInfo.process) {
          // New process-based tunnel cleanup
          console.log(`[LOG] Terminating SSH tunnel process for VM ${body.vmId}`);
          try {
            tunnelInfo.process.kill("SIGTERM");
            console.log(`[LOG] SSH tunnel process terminated for VM ${body.vmId}`);
          } catch (killErr) {
            console.warn(`[WARN] Failed to kill tunnel process:`, killErr.message);

            // Fallback to pkill
            const pkillResult = await runCommand("pkill", [
              "-f",
              `${tunnelInfo.bastionPort}:localhost:`,
            ]);
            if (pkillResult.code === 0) {
              console.log(`[LOG] SSH tunnel killed via pkill for VM ${body.vmId}`);
            }
          }
        } else if (typeof tunnelInfo === 'string') {
          // Legacy string-based tunnel cleanup
          const pkillResult = await runCommand("pkill", [
            "-f",
            tunnelInfo,
          ]);
          if (pkillResult.code === 0) {
            console.log(`[LOG] SSH tunnel for VM ${body.vmId} killed`);
          } else {
            console.warn(
              `[WARN] pkill for VM ${body.vmId} returned code ${pkillResult.code}: ${pkillResult.stderr}`,
            );
          }
        }
      } catch (err) {
        console.error(
          `[ERROR] Failed to kill SSH tunnel for VM ${body.vmId}: ${err.message}`,
        );
      }
      delete runningTunnels[body.vmId];
    }

    // Check if container exists
    const exists = await containerExists(body.vmId);
    if (!exists) {
      return c.json({
        error: "VM not found",
        details: `Container ${body.vmId} does not exist`,
      }, 404);
    }

    const deleteResult = await runCommand("docker", ["rm", "-f", body.vmId]);

    if (deleteResult.code !== 0) {
      console.error(`[ERROR] Docker delete failed`);
      console.error(`[ERROR] Delete stderr: ${deleteResult.stderr}`);
      console.error(`[ERROR] Delete stdout: ${deleteResult.stdout}`);
      return c.json({
        error: "Failed to delete VM",
        details: deleteResult.stderr,
      }, 500);
    }

    return c.json({
      message: "VM deleted successfully",
    }, 200);
  } catch (error) {
    console.error(`[ERROR] VM delete failed: ${error.message}`);
    return c.json({ error: "VM delete failed", details: error.message }, 500);
  }
});

// Configurations for different app types
interface AppConfig {
  image: string;
  containerPort: number;
  requiresDB?: boolean;
  dbImage?: string;
  envVars?: Record<string, string>;
  volumes?: string[];
  dbEnvVars?: Record<string, string>;
  main: {
    cpu: number;
    memory: string;
  };
  database?: {
    cpu: number;
    memory: string;
  };
}

const APP_CONFIGS: Record<string, AppConfig> = {
  odoo: {
    image: "odoo:16",
    containerPort: 8069,
    requiresDB: true,
    dbImage: "postgres:13",
    envVars: {
      HOST: "db",
      USER: "odoo",
      PASSWORD: "myodoo",
      DB_HOST: "db",
      DB_PORT: "5432",
      DB_USER: "odoo",
      DB_PASSWORD: "myodoo",
      DB_NAME: "postgres",
    },
    dbEnvVars: {
      POSTGRES_DB: "postgres",
      POSTGRES_PASSWORD: "myodoo",
      POSTGRES_USER: "odoo",
    },
    main: {
      cpu: 1,
      memory: "1g",
    },
  },
  wordpress: {
    image: "wordpress:latest",
    containerPort: 80,
    requiresDB: true,
    dbImage: "mysql:5.7",
    envVars: {
      WORDPRESS_DB_HOST: "db",
      WORDPRESS_DB_USER: "wordpress",
      WORDPRESS_DB_PASSWORD: "wordpress",
      WORDPRESS_DB_NAME: "wordpress",
      WORDPRESS_TABLE_PREFIX: "wp_",
    },
    dbEnvVars: {
      MYSQL_DATABASE: "wordpress",
      MYSQL_USER: "wordpress",
      MYSQL_PASSWORD: "wordpress",
      MYSQL_ROOT_PASSWORD: "somewordpress",
      MYSQL_INITDB_SKIP_TZINFO: "1", // Speed up initialization
    },
    main: {
      cpu: 1,
      memory: "512m",
    },
    database: {
      cpu: 1,
      memory: "512m",
    },
  },
  libreoffice: {
    image: "collabora/code:latest",
    containerPort: 9980,
    envVars: {
      "domain": "localhost",
      "username": "admin",
      "password": "S3cret",
      "extra_params": "--o:ssl.enable=false",
      "DONT_GEN_SSL_CERT": "1",
    },
    volumes: [
      "/opt/cool/systemplate:/opt/cool/systemplate",
      "/opt/cool/child-roots:/opt/cool/child-roots",
    ],
    main: {
      cpu: 1,
      memory: "512m",
    },
  },
  nextcloud: {
    image: "nextcloud:latest",
    containerPort: 80,
    requiresDB: true,
    dbImage: "mariadb:10.6",
    envVars: {
      MYSQL_HOST: "db",
      MYSQL_DATABASE: "nextcloud",
      MYSQL_USER: "nextcloud",
      MYSQL_PASSWORD: "nextcloud",
      NEXTCLOUD_ADMIN_USER: "admin",
      NEXTCLOUD_ADMIN_PASSWORD: "admin123",
      NEXTCLOUD_TRUSTED_DOMAINS: "*",
    },
    dbEnvVars: {
      MYSQL_ROOT_PASSWORD: "nextcloud_root",
      MYSQL_DATABASE: "nextcloud",
      MYSQL_USER: "nextcloud",
      MYSQL_PASSWORD: "nextcloud",
    },
    main: {
      cpu: 1,
      memory: "1g",
    },
  },
} as const;

// Create AppDeployment
async function createAppDeployment(
  deploymentId: string,
  appType: string,
  networkName: string,
  config: AppConfig,
): Promise<{ containers: any[] }> {
  try {
    console.log(`[LOG] Creating deployment ${deploymentId}`);

    // Create network
    const networkResult = await runCommand("docker", [
      "network",
      "create",
      networkName,
    ]);

    if (networkResult.code !== 0) {
      throw new Error(`Failed to create network: ${networkResult.stderr}`);
    }

    const containers: any[] = [];

    // Start database container if needed
    if (config.requiresDB && config.dbImage) {
      const dbContainerId = `${deploymentId}-db`;
      console.log(`[LOG] Starting database container ${dbContainerId}`);

      const dbResult = await runCommand("docker", [
        "run",
        "-d",
        "--network",
        networkName,
        "--network-alias",
        "db",
        "--name",
        dbContainerId,
        ...Object.entries(config.dbEnvVars || {}).flatMap((
          [k, v],
        ) => ["-e", `${k}=${v}`]),
        `--cpus=${config.database?.cpu || 1}`,
        `--memory=${config.database?.memory || "512m"}`,
        config.dbImage,
      ]);

      if (dbResult.code !== 0) {
        throw new Error(
          `Failed to start database container: ${dbResult.stderr}`,
        );
      }

      containers.push({
        id: dbContainerId,
        type: "database",
        port: null,
      });
    }

    // Start main application container
    const mainContainerId = `${deploymentId}-main`;
    const mainPort = await findFreePort(
      PORT_RANGES[appType].start,
      PORT_RANGES[appType].end,
    );

    console.log(
      `[LOG] Starting main container ${mainContainerId} on port ${mainPort}`,
    );

    const mainResult = await runCommand("docker", [
      "run",
      "-d",
      "--network",
      networkName,
      "--name",
      mainContainerId,
      ...Object.entries(config.envVars || {}).flatMap((
        [k, v],
      ) => ["-e", `${k}=${v}`]),
      "-p",
      `${mainPort}:${config.containerPort}`,
      `--cpus=${config.main.cpu}`,
      `--memory=${config.main.memory}`,
      config.image,
    ]);

    if (mainResult.code !== 0) {
      throw new Error(`Failed to start main container: ${mainResult.stderr}`);
    }

    containers.push({
      id: mainContainerId,
      type: "main",
      port: mainPort,
    });

    // --- SSH/HTTP Tunneling for Deployments (TODO) ---
    // When you want to expose the main app container (e.g., WordPress, Odoo) via the bastion,
    // you should request a tunnel from the Bastion Manager and start a reverse SSH tunnel
    // from the provider to the bastion for the app's port, just like for VMs.
    //
    // Example (to be integrated after main container is started in createAppDeployment):
    //
    // TODO: Integrate this block after main container startup for deployment exposure
    /*
    const bastionManagerUrl = Deno.env.get("BASTION_MANAGER_URL");
    const providerPublicKey = Deno.env.get("PROVIDER_PUBLIC_KEY");
    const mainContainerId = `${deploymentId}-main`;
    // Assume mainPort is the local port mapped to the app (e.g., 8080 for WordPress)
    try {
      const tunnelRes = await fetch(`${bastionManagerUrl}/request-tunnel`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          provider_container_id: mainContainerId,
          app_port: mainPort, // <-- new field for app port
          tunnel_type: "http", // or "tcp"
        }),
      });
      const tunnelInfo = await tunnelRes.json();
      if (!tunnelRes.ok || !tunnelInfo.port || !tunnelInfo.bastion_ip) {
        throw new Error(
          typeof tunnelInfo.error === "string"
            ? tunnelInfo.error
            : "Failed to assign tunnel",
        );
      }
      // Start the reverse SSH tunnel for HTTP
      const sshTunnelCmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-i", Deno.env.get("PROVIDER_PRIVATE_KEY") || "/root/.ssh/id_rsa",
        "-N",
        "-R", `${tunnelInfo.port}:localhost:${mainPort}`,
        `${tunnelInfo.bastion_ssh_user || "ubuntu"}@${tunnelInfo.bastion_ip}`,
      ];
      runningTunnels[mainContainerId] = sshTunnelCmd.join(" ");
      await runCommand(sshTunnelCmd[0], sshTunnelCmd.slice(1));
      // You can now expose the app at http://<bastion_ip>:<tunnelInfo.port>
    } catch (err) {
      console.error(`[ERROR] Failed to start app tunnel:`, err);
      // Handle error as needed
    }
    */
    // --- END SSH/HTTP Tunneling for Deployments (TODO) ---

    return { containers };
  } catch (error) {
    console.error(`[ERROR] Deployment failed: ${error.message}`);
    await cleanupDeployment(deploymentId, networkName);
    throw error;
  }
}

// Create App Deployment with it other releated containers
app.post("/create-app", async (c) => {
  try {
    const body: AppRequest = await c.req.json();
    const { type, deploymentId } = body;

    console.log(
      `[LOG] Creating application of type ${type} with ID ${deploymentId}`,
    );

    const config = APP_CONFIGS[type];
    if (!config) {
      return c.json({ error: "Unsupported app type" }, 400);
    }

    const networkName = `${deploymentId}-network`;

    // Create the deployment
    const result = await createAppDeployment(
      deploymentId,
      type,
      networkName,
      config,
    );

    return c.json({
      deploymentId,
      networkName,
      containers: result.containers,
    });
  } catch (error) {
    console.error(`[ERROR] Create-app failed: ${error.message}`);
    return c.json({
      error: "Failed to create application",
      details: error.message,
    }, 500);
  }
});

// Start Deployment containers
app.post("/start-deployment", async (c) => {
  try {
    const { deploymentId } = await c.req.json();

    // Check if network exists
    const networkExists = await checkDeploymentNetwork(deploymentId);
    if (!networkExists) {
      return c.json({
        error: "Deployment network not found",
        details: `Network ${deploymentId}-network does not exist`,
      }, 404);
    }

    // Get containers and verify they exist
    let containers;
    try {
      containers = await getDeploymentContainers(deploymentId);
    } catch (error) {
      return c.json({
        error: "Deployment not found",
        details: error.message,
      }, 404);
    }

    // Start containers in correct order (database first)
    for (const container of containers) {
      const startResult = await runCommand("docker", ["start", container.id]);
      if (startResult.code !== 0) {
        return c.json({
          error: "Failed to start container",
          details: `Failed to start ${container.id}: ${startResult.stderr}`,
        }, 500);
      }
      console.log(`[LOG] Started container ${container.id}`);
    }

    return c.json({
      status: "started",
      deploymentId,
      containers: containers.map((c) => ({
        id: c.id,
        type: c.type,
        status: "running",
      })),
    });
  } catch (error) {
    return c.json({
      error: "Failed to start deployment",
      details: error.message,
    }, 500);
  }
});

// Stop Deployment containers
app.post("/stop-deployment", async (c) => {
  try {
    const { deploymentId } = await c.req.json();

    let containers;
    try {
      containers = await getDeploymentContainers(deploymentId);
    } catch (error) {
      return c.json({
        error: "Deployment not found",
        details: error.message,
      }, 404);
    }

    // Stop in reverse order (main container first, then dependencies)
    for (const container of containers.reverse()) {
      const stopResult = await runCommand("docker", ["stop", container.id]);
      if (stopResult.code !== 0) {
        return c.json({
          error: "Failed to stop container",
          details: `Failed to stop ${container.id}: ${stopResult.stderr}`,
        }, 500);
      }
      console.log(`[LOG] Stopped container ${container.id}`);
    }

    return c.json({
      status: "stopped",
      deploymentId,
      containers: containers.map((c) => ({
        id: c.id,
        type: c.type,
        status: "stopped",
      })),
    });
  } catch (error) {
    return c.json({
      error: "Failed to stop deployment",
      details: error.message,
    }, 500);
  }
});

// Delete Deployment containers
app.post("/delete-deployment", async (c) => {
  try {
    const { deploymentId } = await c.req.json();
    const networkName = `${deploymentId}-network`;

    let containers;
    try {
      containers = await getDeploymentContainers(deploymentId);
    } catch (error) {
      // If no containers found, just try to remove network
      await runCommand("docker", ["network", "rm", networkName]).catch(
        () => {},
      );
      return c.json({
        error: "Deployment not found",
        details: error.message,
      }, 404);
    }

    // Stop and remove containers
    for (const container of containers) {
      await runCommand("docker", ["stop", container.id]).catch(() => {});
      await runCommand("docker", ["rm", container.id]).catch(() => {});
      console.log(`[LOG] Removed container ${container.id}`);
    }

    // Remove network
    await runCommand("docker", ["network", "rm", networkName]).catch(() => {});
    console.log(`[LOG] Removed network ${networkName}`);

    return c.json({
      status: "deleted",
      deploymentId,
      details: "Deployment and all associated resources have been removed",
    });
  } catch (error) {
    return c.json({
      error: "Failed to delete deployment",
      details: error.message,
    }, 500);
  }
});

// Cleanup Deployment
async function cleanupDeployment(
  deploymentId: string,
  networkName: string,
): Promise<void> {
  const containers = await getDeploymentContainers(deploymentId);

  // Stop and remove containers
  for (const container of containers) {
    await runCommand("docker", ["stop", container.id]).catch(() => {});
    await runCommand("docker", ["rm", container.id]).catch(() => {});
  }

  // Remove network
  await runCommand("docker", ["network", "rm", networkName]).catch(() => {});
}

// Cleanup Unused networks
async function cleanupNetworks() {
  try {
    const result = await runCommand("docker", ["network", "prune", "-f"]);
    if (result.code === 0) {
      console.log("[LOG] Successfully cleaned up unused networks");
    }
  } catch (error) {
    console.error("[ERROR] Network cleanup failed:", error.message);
  }
}

// Cleanup Unused Resources
export async function cleanupUnusedResources() {
  try {
    console.log("[LOG] Starting cleanup of unused resources");

    // Remove stopped containers older than 24h
    const containerPrune = await runCommand("docker", [
      "container",
      "prune",
      "-f",
      "--filter",
      "until=24h",
    ]);

    if (containerPrune.code === 0) {
      console.log("[LOG] Successfully cleaned up old containers");
    }

    // Remove unused images
    const imagePrune = await runCommand("docker", [
      "image",
      "prune",
      "-f",
    ]);

    if (imagePrune.code === 0) {
      console.log("[LOG] Successfully cleaned up unused images");
    }

    await cleanupNetworks();
  } catch (error) {
    console.error("[ERROR] Cleanup failed:", error.message);
  }
}

async function getDeploymentContainers(
  deploymentId: string,
): Promise<Array<{ id: string; type: string }>> {
  try {
    // First check if any containers exist with this deployment ID
    const ps = await runCommand("docker", [
      "ps",
      "-a",
      "--filter",
      `name=${deploymentId}`,
      "--format",
      "{{.Names}}",
    ]);

    if (ps.code !== 0) {
      throw new Error(`Failed to list containers: ${ps.stderr}`);
    }

    const containerNames = ps.stdout.trim().split("\n").filter((name) => name);

    if (containerNames.length === 0) {
      throw new Error(`No containers found for deployment: ${deploymentId}`);
    }

    // Verify each container actually exists
    const containers = [];
    for (const name of containerNames) {
      const inspect = await runCommand("docker", ["inspect", name]);
      if (inspect.code !== 0) {
        console.error(`[ERROR] Container ${name} not found`);
        continue;
      }

      containers.push({
        id: name,
        type: name.endsWith("-db") ? "database" : "main",
      });
    }

    if (containers.length === 0) {
      throw new Error(
        `No valid containers found for deployment: ${deploymentId}`,
      );
    }

    // Sort containers so database is handled appropriately
    return containers.sort((a, b) => {
      if (a.type === "database") return -1;
      if (b.type === "database") return 1;
      return 0;
    });
  } catch (error) {
    console.error(
      `[ERROR] Failed to get deployment containers: ${error.message}`,
    );
    throw error;
  }
}

async function checkDeploymentNetwork(deploymentId: string): Promise<boolean> {
  const networkName = `${deploymentId}-network`;
  const result = await runCommand("docker", [
    "network",
    "inspect",
    networkName,
  ]);
  return result.code === 0;
}

// --- App Port Tunneling for VMs (TODO) ---
// To expose apps (web servers, databases, etc.) running inside a VM created via /create-vm,
// you need to request a tunnel for each app port from the Bastion Manager and start a reverse SSH tunnel
// from the provider (or from inside the VM) to the bastion for that port.
//
// Example usage (to be integrated after app/service is started inside the VM):
//
// TODO: Integrate this block after app/service startup inside the VM to enable public access
/*
const bastionManagerUrl = Deno.env.get("BASTION_MANAGER_URL");
const providerPrivateKey = Deno.env.get("PROVIDER_PRIVATE_KEY");
const vmId = "<your-vm-id>";
const appPort = <your-app-port>; // e.g., 8080 for a web server

try {
  const tunnelRes = await fetch(`${bastionManagerUrl}/request-tunnel`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      provider_container_id: vmId,
      app_port: appPort, // <-- new field for app port
      tunnel_type: "http", // or "tcp"
    }),
  });
  const tunnelInfo = await tunnelRes.json();
  if (!tunnelRes.ok || !tunnelInfo.port || !tunnelInfo.bastion_ip) {
    throw new Error(
      typeof tunnelInfo.error === "string"
        ? tunnelInfo.error
        : "Failed to assign tunnel",
    );
  }
  // Start the reverse SSH tunnel for the app port
  const sshTunnelCmd = [
    "ssh",
    "-o", "StrictHostKeyChecking=no",
    "-i", providerPrivateKey || "/root/.ssh/id_rsa",
    "-N",
    "-R", `${tunnelInfo.port}:localhost:${appPort}`,
    `${tunnelInfo.bastion_ssh_user || "ubuntu"}@${tunnelInfo.bastion_ip}`,
  ];
  runningTunnels[`${vmId}-app-${appPort}`] = sshTunnelCmd.join(" ");
  await runCommand(sshTunnelCmd[0], sshTunnelCmd.slice(1));
  // The app is now accessible at http://<bastion_ip>:<tunnelInfo.port>
} catch (err) {
  console.error(`[ERROR] Failed to start app tunnel for VM:`, err);
  // Handle error as needed
}
*/
// --- END App Port Tunneling for VMs (TODO) ---
