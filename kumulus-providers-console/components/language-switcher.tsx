"use client";

import { useState } from 'react';
import { useTranslations, useLocale, useSetLocale } from '@/lib/i18n-context';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Globe } from 'lucide-react';

const locales = ['en', 'fr'] as const;

export function LanguageSwitcher() {
  const t = useTranslations();
  const locale = useLocale();
  const setLocale = useSetLocale();
  const [isUpdating, setIsUpdating] = useState(false);

  const switchLanguage = async (newLocale: 'en' | 'fr') => {
    if (newLocale === locale) return;

    setIsUpdating(true);

    try {
      // This will update both localStorage and database
      await setLocale(newLocale);
    } catch (error) {
      console.error('Failed to update language preference:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" disabled={isUpdating}>
          <Globe className="h-4 w-4 mr-2" />
          {t(`languages.${locale}`)}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {locales.map((lang) => (
          <DropdownMenuItem
            key={lang}
            onClick={() => switchLanguage(lang)}
            className={locale === lang ? 'bg-accent' : ''}
          >
            {t(`languages.${lang}`)}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
