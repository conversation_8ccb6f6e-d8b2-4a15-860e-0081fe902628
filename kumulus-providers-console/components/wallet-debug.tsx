'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface DebugInfo {
  error?: string;
  injectedWeb3?: unknown;
  polkadotExtension?: unknown;
  talismanEth?: unknown;
  userAgent?: string;
  availableExtensions?: string[];
}

export function WalletDebug() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({});
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const checkExtensions = () => {
    if (typeof window === 'undefined') {
      setDebugInfo({ error: 'Not in browser environment' });
      return;
    }

    const windowWithExtensions = window as Window & {
      injectedWeb3?: Record<string, unknown>;
      polkadotExtension?: unknown;
      talismanEth?: unknown;
    };

    const info: DebugInfo = {
      injectedWeb3: windowWithExtensions.injectedWeb3,
      polkadotExtension: windowWithExtensions.polkadotExtension,
      talismanEth: windowWithExtensions.talismanEth,
      userAgent: navigator.userAgent,
      availableExtensions: Object.keys(windowWithExtensions.injectedWeb3 || {}),
    };

    setDebugInfo(info);
  };

  const testPolkadotImport = async () => {
    try {
      const { web3Enable, web3Accounts } = await import('@polkadot/extension-dapp');
      console.log('Polkadot extension-dapp imported successfully');
      
      const extensions = await web3Enable('Kumulus Debug');
      console.log('Extensions enabled:', extensions);
      
      const accounts = await web3Accounts();
      console.log('Accounts:', accounts);
      
      setDebugInfo(prev => ({
        ...prev,
        importSuccess: true,
        extensions,
        accounts
      }));
    } catch (error) {
      console.error('Import error:', error);
      setDebugInfo(prev => ({
        ...prev,
        importError: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  };

  if (!isClient) {
    return <div>Loading...</div>;
  }

  return (
    <div className="p-4 border rounded-lg space-y-4">
      <h3 className="text-lg font-semibold">Wallet Debug Information</h3>
      
      <div className="space-y-2">
        <Button onClick={checkExtensions}>Check Extensions</Button>
        <Button onClick={testPolkadotImport}>Test Polkadot Import</Button>
      </div>
      
      <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
        {JSON.stringify(debugInfo, null, 2)}
      </pre>
    </div>
  );
}
