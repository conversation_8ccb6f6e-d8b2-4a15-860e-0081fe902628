"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';

type Locale = 'en' | 'fr';

interface I18nContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string, params?: Record<string, string>) => string;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

// Import translation files
const translations = {
  en: {
    "common.loading": "Loading...",
    "common.save": "Save",
    "common.cancel": "Cancel",
    "common.refresh": "Refresh",
    "common.error": "An error occurred",
    "common.success": "Success",
    "common.failed": "Failed",
    "common.back": "Back",
    "common.retry": "Retry",
    "navigation.dashboard": "Dashboard",
    "navigation.resources": "Resources",
    "navigation.profile": "Profile",
    "navigation.settings": "Settings",
    "navigation.kumulus": "Kumulus",
    "auth.login.title": "Login to your account",
    "auth.login.description": "Enter your email below to login to your account",
    "auth.login.email": "Email",
    "auth.login.password": "Password",
    "auth.login.name": "Name",
    "auth.login.signIn": "Sign In",
    "auth.login.signUp": "Sign Up",
    "auth.login.createAccount": "Create an account",
    "auth.login.createAccountDescription": "Enter your details below to create your account",
    "auth.login.namePlaceholder": "John Doe",
    "dashboard.title": "Dashboard",
    "dashboard.welcomeBack": "Welcome back, {name}",
    "dashboard.loadingDashboard": "Loading dashboard...",
    "dashboard.errorLoading": "Error Loading Dashboard",
    "dashboard.noData": "No dashboard data available",
    "dashboard.totalResources": "Total Resources",
    "dashboard.activeResources": "Active Resources",
    "dashboard.totalEarnings": "Total Earnings",
    "dashboard.utilizationRate": "Utilization Rate",
    "dashboard.recentActivity": "Recent Activity",
    "dashboard.providerStatus": "Provider Status",
    "dashboard.registeredResources": "Registered computing resources",
    "dashboard.currentlyRunning": "Currently running and earning",
    "dashboard.lifetimeEarnings": "Lifetime earnings from resources",
    "dashboard.averageUtilization": "Average resource utilization",
    "dashboard.latestUpdates": "Latest updates from your computing resources",
    "dashboard.noRecentActivity": "No recent activity",
    "dashboard.performanceOverview": "Performance Overview",
    "dashboard.performanceDescription": "Your resource performance and efficiency metrics",
    "dashboard.totalHours": "Total Hours Available",
    "dashboard.activeHours": "Active Hours",
    "dashboard.efficiency": "Efficiency Rate",
    "profile.title": "Profile",
    "profile.loadingProfile": "Loading profile...",
    "profile.errorLoading": "Error Loading Profile",
    "profile.manageAccount": "Manage your account settings and preferences",
    "resources.title": "Resources",
    "resources.loadingResources": "Loading resources...",
    "resources.errorLoading": "Error Loading Resources",
    "resources.noResources": "No resources registered yet",
    "resources.add.title": "Add Resource",
    "resources.add.description": "Register a new computing resource to the Kumulus network",
    "resources.add.installCommand": "Install Command",
    "resources.add.instructions": "Run this command on your server to install the Kumulus provider agent:",
    "resources.add.requirements": "Requirements:",
    "resources.add.nextSteps": "Next Steps:",
    "resources.add.manualSetup": "Manual Setup",
    "resources.add.manualDescription": "Prefer to set up manually? Download and configure the agent yourself.",
    "resources.add.downloadAgent": "Download Agent",
    "resources.add.viewDocs": "View Documentation",
    "resources.details.title": "Resource Details",
    "resources.details.error": "Error Loading Resource",
    "resources.details.notFound": "Resource not found",
    "resources.details.status": "Status",
    "resources.details.specifications": "Specifications",
    "resources.details.actions": "Actions",
    "resources.details.configure": "Configure",
    "resources.details.remove": "Remove",
    "resources.status.active": "Active",
    "resources.status.ready": "Ready",
    "resources.status.installing": "Installing",
    "resources.status.pending": "Pending",
    "resources.status.error": "Error",
    "resources.status.inactive": "Inactive",
    "settings.title": "Settings",
    "settings.loadingSettings": "Loading settings...",
    "settings.errorLoading": "Error Loading Settings",
    "settings.managePreferences": "Manage your account preferences and security settings",
    "languages.en": "English",
    "languages.fr": "Français"
  },
  fr: {
    "common.loading": "Chargement...",
    "common.save": "Enregistrer",
    "common.cancel": "Annuler",
    "common.refresh": "Actualiser",
    "common.error": "Une erreur s'est produite",
    "common.success": "Succès",
    "common.failed": "Échec",
    "common.back": "Retour",
    "common.retry": "Réessayer",
    "navigation.dashboard": "Tableau de bord",
    "navigation.resources": "Ressources",
    "navigation.profile": "Profil",
    "navigation.settings": "Paramètres",
    "navigation.kumulus": "Kumulus",
    "auth.login.title": "Connectez-vous à votre compte",
    "auth.login.description": "Entrez votre email ci-dessous pour vous connecter à votre compte",
    "auth.login.email": "Email",
    "auth.login.password": "Mot de passe",
    "auth.login.name": "Nom",
    "auth.login.signIn": "Se connecter",
    "auth.login.signUp": "S'inscrire",
    "auth.login.createAccount": "Créer un compte",
    "auth.login.createAccountDescription": "Entrez vos détails ci-dessous pour créer votre compte",
    "auth.login.namePlaceholder": "Jean Dupont",
    "dashboard.title": "Tableau de bord",
    "dashboard.welcomeBack": "Bon retour, {name}",
    "dashboard.loadingDashboard": "Chargement du tableau de bord...",
    "dashboard.errorLoading": "Erreur de chargement du tableau de bord",
    "dashboard.noData": "Aucune donnée de tableau de bord disponible",
    "dashboard.totalResources": "Ressources totales",
    "dashboard.activeResources": "Ressources actives",
    "dashboard.totalEarnings": "Gains totaux",
    "dashboard.utilizationRate": "Taux d'utilisation",
    "dashboard.recentActivity": "Activité récente",
    "dashboard.providerStatus": "Statut du fournisseur",
    "dashboard.registeredResources": "Ressources informatiques enregistrées",
    "dashboard.currentlyRunning": "Actuellement en cours d'exécution et de gain",
    "dashboard.lifetimeEarnings": "Gains à vie des ressources",
    "dashboard.averageUtilization": "Utilisation moyenne des ressources",
    "dashboard.latestUpdates": "Dernières mises à jour de vos ressources informatiques",
    "dashboard.noRecentActivity": "Aucune activité récente",
    "dashboard.performanceOverview": "Aperçu des performances",
    "dashboard.performanceDescription": "Vos métriques de performance et d'efficacité des ressources",
    "dashboard.totalHours": "Total d'heures disponibles",
    "dashboard.activeHours": "Heures actives",
    "dashboard.efficiency": "Taux d'efficacité",
    "profile.title": "Profil",
    "profile.loadingProfile": "Chargement du profil...",
    "profile.errorLoading": "Erreur de chargement du profil",
    "profile.manageAccount": "Gérez les paramètres et préférences de votre compte",
    "resources.title": "Ressources",
    "resources.loadingResources": "Chargement des ressources...",
    "resources.errorLoading": "Erreur de chargement des ressources",
    "resources.noResources": "Aucune ressource enregistrée pour le moment",
    "resources.add.title": "Ajouter une ressource",
    "resources.add.description": "Enregistrer une nouvelle ressource informatique sur le réseau Kumulus",
    "resources.add.installCommand": "Commande d'installation",
    "resources.add.instructions": "Exécutez cette commande sur votre serveur pour installer l'agent fournisseur Kumulus :",
    "resources.add.requirements": "Exigences :",
    "resources.add.nextSteps": "Prochaines étapes :",
    "resources.add.manualSetup": "Configuration manuelle",
    "resources.add.manualDescription": "Vous préférez configurer manuellement ? Téléchargez et configurez l'agent vous-même.",
    "resources.add.downloadAgent": "Télécharger l'agent",
    "resources.add.viewDocs": "Voir la documentation",
    "resources.details.title": "Détails de la ressource",
    "resources.details.error": "Erreur de chargement de la ressource",
    "resources.details.notFound": "Ressource non trouvée",
    "resources.details.status": "Statut",
    "resources.details.specifications": "Spécifications",
    "resources.details.actions": "Actions",
    "resources.details.configure": "Configurer",
    "resources.details.remove": "Supprimer",
    "resources.status.active": "Actif",
    "resources.status.ready": "Prêt",
    "resources.status.installing": "Installation",
    "resources.status.pending": "En attente",
    "resources.status.error": "Erreur",
    "resources.status.inactive": "Inactif",
    "settings.title": "Paramètres",
    "settings.loadingSettings": "Chargement des paramètres...",
    "settings.errorLoading": "Erreur de chargement des paramètres",
    "settings.managePreferences": "Gérez vos préférences de compte et paramètres de sécurité",
    "languages.en": "English",
    "languages.fr": "Français"
  }
};

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>('en');

  // Load locale from localStorage on mount
  useEffect(() => {
    const savedLocale = localStorage.getItem('kumulus-locale') as Locale;
    if (savedLocale && (savedLocale === 'en' || savedLocale === 'fr')) {
      setLocaleState(savedLocale);
    }
  }, []);

  const setLocale = async (newLocale: Locale) => {
    setLocaleState(newLocale);
    localStorage.setItem('kumulus-locale', newLocale);
    
    // Also update in database if user is logged in
    try {
      await fetch('http://localhost:8000/api/profile/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          preferences: {
            language: newLocale,
          },
        }),
      });
    } catch (error) {
      console.error('Failed to update language preference in database:', error);
    }
  };

  const t = (key: string, params?: Record<string, string>): string => {
    const translation = translations[locale][key as keyof typeof translations[typeof locale]] || key;
    
    if (params) {
      return Object.entries(params).reduce((str, [paramKey, paramValue]) => {
        return str.replace(`{${paramKey}}`, paramValue);
      }, translation);
    }
    
    return translation;
  };

  return (
    <I18nContext.Provider value={{ locale, setLocale, t }}>
      {children}
    </I18nContext.Provider>
  );
}

export function useTranslations() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useTranslations must be used within an I18nProvider');
  }
  return context.t;
}

export function useLocale() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useLocale must be used within an I18nProvider');
  }
  return context.locale;
}

export function useSetLocale() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useSetLocale must be used within an I18nProvider');
  }
  return context.setLocale;
}
