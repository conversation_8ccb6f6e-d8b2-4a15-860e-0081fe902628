import { BACKEND_URL } from "@/app/constants";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const fetchJWTToken = async () => {
  const response = await fetch(`${BACKEND_URL}/api/auth/token`, {
    credentials: "include",
  });
  //handle error 
  if (!response.ok) { 
    throw new Error("Failed to fetch token");
  }
  const data = await response.json();
  return data.token;
};
