// Simple in-memory data store for mock resources
// In a real application, this would be replaced with a database

export interface Resource {
  id: string;
  resource_name: string;
  cpu_cores_offered: number;
  ram_gb_offered: number;
  storage_gb_offered: number;
  gpu_available: boolean;
  gpu_count?: number;
  gpu_vram_gb?: number;
  status: "pending" | "installing" | "ready" | "active" | "error";
  created_at: string;
  updated_at: string;
}

// Mock data store
const mockResources: Resource[] = [];

export const mockDataStore = {
  // Get all resources
  getAllResources: (): Resource[] => {
    return mockResources;
  },

  // Get resource by ID
  getResourceById: (id: string): Resource | undefined => {
    return mockResources.find((resource) => resource.id === id);
  },

  // Add new resource
  addResource: (resource: Resource): Resource => {
    mockResources.push(resource);
    return resource;
  },

  // Update resource
  updateResource: (id: string, updates: Partial<Resource>): Resource | null => {
    const index = mockResources.findIndex((resource) => resource.id === id);
    if (index !== -1) {
      mockResources[index] = {
        ...mockResources[index],
        ...updates,
        updated_at: new Date().toISOString(),
      };
      return mockResources[index];
    }
    return null;
  },

  // Delete resource
  deleteResource: (id: string): boolean => {
    const index = mockResources.findIndex((resource) => resource.id === id);
    if (index !== -1) {
      mockResources.splice(index, 1);
      return true;
    }
    return false;
  },

  // Simulate installation process (for demo purposes)
  simulateInstallation: (id: string): void => {
    const resource = mockResources.find((r) => r.id === id);
    if (resource && resource.status === "pending") {
      // Set to installing after a short delay
      setTimeout(() => {
        mockDataStore.updateResource(id, { status: "installing" });
      }, 2000);

      // After 8 seconds total, set to ready (simulating installation completion)
      setTimeout(() => {
        mockDataStore.updateResource(id, { status: "ready" });
      }, 8000);
    }
  },
};
