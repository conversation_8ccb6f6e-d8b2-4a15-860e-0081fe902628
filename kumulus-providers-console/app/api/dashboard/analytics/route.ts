import { NextResponse } from "next/server";
import { mockDataStore } from "@/lib/mock-data";

export async function GET() {
  try {
    const resources = mockDataStore.getAllResources();

    // Mock analytics data for provider dashboard
    const analyticsData = {
      analytics: {
        earningsOverTime: [
          { date: "2024-01-01", earnings: 45.20 },
          { date: "2024-01-02", earnings: 52.80 },
          { date: "2024-01-03", earnings: 38.90 },
          { date: "2024-01-04", earnings: 67.30 },
          { date: "2024-01-05", earnings: 71.50 },
        ],
        resourceUsage: resources.map((resource) => ({
          resource: resource.resource_name,
          utilization: Math.floor(Math.random() * 40) + 60, // Random utilization between 60-100%
        })),
        performance: {
          totalHours: 2847,
          activeHours: 2234,
          efficiency: "94.2%",
        },
      },
    };

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error("Failed to fetch dashboard analytics:", error);
    return NextResponse.json(
      { message: "Failed to fetch dashboard analytics" },
      { status: 500 },
    );
  }
}
