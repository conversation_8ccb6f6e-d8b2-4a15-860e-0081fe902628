"use client";

import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Cloud,
  RefreshCw,
  XCircle,
} from "lucide-react";

interface ProviderStatus {
  id: string;
  name: string;
  installationStatus: "pending" | "in_progress" | "completed" | "failed";
  agentStatus: "offline" | "starting" | "running" | "error";
  lastHeartbeat: string | null;
  installationProgress: number;
  errorMessage?: string;
}

interface DashboardData {
  stats: {
    totalProviders: number;
    runningProviders: number;
    installingProviders: number;
    failedProviders: number;
    activeConnections: number;
    totalRequests: number;
    uptime: string;
  };
  providerStatus: ProviderStatus[];
  recentActivity: Array<{
    id: number;
    action: string;
    provider: string;
    timestamp: string;
    status: "success" | "error" | "info";
  }>;
}

export function ProviderStatusDashboard() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(
        "http://localhost:8000/api/dashboard/overview",
        {
          credentials: "include",
        },
      );

      if (!response.ok) {
        throw new Error("Failed to fetch dashboard data");
      }

      const data = await response.json();
      setDashboardData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // Poll for updates every 30 seconds
  useEffect(() => {
    fetchDashboardData();

    const interval = setInterval(() => {
      if (!isLoading) {
        fetchDashboardData();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isLoading]);

  const getStatusIcon = (status: ProviderStatus["agentStatus"]) => {
    switch (status) {
      case "running":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "starting":
        return <RefreshCw className="h-4 w-4 text-yellow-600 animate-spin" />;
      case "offline":
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (
    installationStatus: ProviderStatus["installationStatus"],
    agentStatus: ProviderStatus["agentStatus"],
  ) => {
    if (installationStatus === "failed") {
      return <Badge variant="destructive">Failed</Badge>;
    }
    if (installationStatus === "in_progress") {
      return (
        <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
          Installing
        </Badge>
      );
    }
    if (agentStatus === "running") {
      return (
        <Badge className="bg-green-100 text-green-800 border-green-200">
          Online
        </Badge>
      );
    }
    if (agentStatus === "starting") {
      return (
        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
          Starting
        </Badge>
      );
    }
    return <Badge variant="secondary">Offline</Badge>;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">
            Loading provider status...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            Error Loading Provider Status
          </CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={fetchDashboardData} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Provider Status Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Providers
            </CardTitle>
            <Cloud className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData?.stats.totalProviders}
            </div>
            <p className="text-xs text-muted-foreground">
              Registered providers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Running Providers
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {dashboardData?.stats.runningProviders}
            </div>
            <p className="text-xs text-muted-foreground">Agents online</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Installing</CardTitle>
            <RefreshCw className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {dashboardData?.stats.installingProviders}
            </div>
            <p className="text-xs text-muted-foreground">In progress</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed/Errors</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {dashboardData?.stats.failedProviders}
            </div>
            <p className="text-xs text-muted-foreground">Need attention</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Provider Status List */}
        <Card>
          <CardHeader>
            <CardTitle>Provider Status</CardTitle>
            <CardDescription>
              Real-time status of all registered providers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData?.providerStatus.map((provider) => (
                <div
                  key={provider.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(provider.agentStatus)}
                    <div>
                      <p className="text-sm font-medium">{provider.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {provider.installationStatus === "in_progress"
                          ? `Installing... ${provider.installationProgress}%`
                          : provider.lastHeartbeat
                          ? `Last seen: ${
                            new Date(provider.lastHeartbeat)
                              .toLocaleTimeString()
                          }`
                          : "Never connected"}
                      </p>
                      {provider.errorMessage && (
                        <p className="text-xs text-red-600">
                          {provider.errorMessage}
                        </p>
                      )}
                    </div>
                  </div>
                  {getStatusBadge(
                    provider.installationStatus,
                    provider.agentStatus,
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest provider events and status changes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData?.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div
                      className={`h-2 w-2 rounded-full ${
                        activity.status === "success"
                          ? "bg-green-500"
                          : activity.status === "error"
                          ? "bg-red-500"
                          : "bg-blue-500"
                      }`}
                    >
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">{activity.action}</p>
                    <p className="text-xs text-muted-foreground">
                      {activity.provider}
                    </p>
                  </div>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Clock className="h-3 w-3 mr-1" />
                    {new Date(activity.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
