"use client";

import { useEffect, useState } from "react";
import { DashboardLayout } from "@/components/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Cpu,
  HardDrive,
  MemoryStick,
  Monitor,
  Plus,
  RefreshCw,
  Server,
} from "lucide-react";
import Link from "next/link";
import { BACKEND_URL } from "../constants";

// Removed unused Resource2 interface

interface Resource {
  id: string;
  name: string;
  cpu: number;
  ram: number;
  storage: number;
  gpu?: {
    count: number;
    vram: number;
  };
  status:  "pending" | "installing" | "ready" | "active" | "incative"| "error";
  createdAt: string;
  updatedAt: string;
}


interface ResourcesData {
  resources: Resource[];
  total: number;
}

export default function ResourcesPage() {
  const [resourcesData, setResourcesData] = useState<ResourcesData | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);



  const fetchResourcesData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${BACKEND_URL}/api/resources`, {
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to fetch resources data");
      }

      const data = await response.json();

      setResourcesData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchResourcesData();
  }, []);

  const getStatusBadge = (status: Resource["status"]) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />Step 1: Pending Setup
          </Badge>
        );
      case "installing":
        return (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            <RefreshCw className="h-3 w-3 mr-1 animate-spin" />Step 2:
            Installing
          </Badge>
        );
      case "ready":
        return (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />Step 3: Ready to Provide
          </Badge>
        );
      case "active":
        return (
          <Badge variant="default" className="bg-green-600">
            <CheckCircle className="h-3 w-3 mr-1" />Active - Providing
          </Badge>
        );
      case "error":
        return (
          <Badge variant="destructive">
            <AlertCircle className="h-3 w-3 mr-1" />Error
          </Badge>
        );
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  // Removed unused handler functions

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              Loading resources...
            </p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading Resources
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchResourcesData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </DashboardLayout>
    );
  }

  // No resources state
  if (!resourcesData?.resources || resourcesData.resources.length === 0) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Resources</h1>
              <p className="text-muted-foreground">
                Manage your computing resources and infrastructure
              </p>
            </div>
            <Button onClick={fetchResourcesData} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          {/* Empty State */}
          <Card className="text-center py-12">
            <CardContent>
              <Server className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">
                No resources registered
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Get started by adding your first computing resource. You can
                register servers, workstations, or any machine you want to
                contribute to the network.
              </p>
              <Link href="/resources/add">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Resource
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Resources list state
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Resources</h1>
            <p className="text-muted-foreground">
              {resourcesData.total}{" "}
              resource{resourcesData.total !== 1 ? "s" : ""} registered
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={fetchResourcesData} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Link href="/resources/add">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Resource
              </Button>
            </Link>
          </div>
        </div>

        {/* Resources Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {resourcesData.resources.map((resource) => (
            <Card
              key={resource.id}
              className="hover:shadow-md transition-shadow"
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    {resource.name}
                  </CardTitle>
                  {getStatusBadge(resource.status)}
                </div>
                <CardDescription>
                  Added {new Date(resource.createdAt).toLocaleDateString()}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    <Cpu className="h-4 w-4 text-muted-foreground" />
                    <span>{resource.cpu} CPU cores</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <MemoryStick className="h-4 w-4 text-muted-foreground" />
                    <span>{resource.ram} GB RAM</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <HardDrive className="h-4 w-4 text-muted-foreground" />
                    <span>{resource.storage} GB storage</span>
                  </div>
                  {resource.gpu && (
                    <div className="flex items-center gap-2 text-sm">
                      <Monitor className="h-4 w-4 text-muted-foreground" />
                      <span>
                        {resource.gpu.count}{" "}
                        GPU{resource.gpu.count !== 1 ? "s" : ""}{" "}
                        ({resource.gpu.vram} GB VRAM)
                      </span>
                    </div>
                  )}
                </div>

                {/* Standardized View Resource Button */}
                <div className="mt-4">
                  <Link href={`/resources/${resource.id}`}>
                    <Button variant="outline" size="sm" className="w-full">
                      View Resource
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </DashboardLayout>
  );
}
