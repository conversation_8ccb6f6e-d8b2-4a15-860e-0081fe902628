"use client";

import { useEffect, useState, useCallback } from "react";
import { useParams } from "next/navigation";
import { DashboardLayout } from "@/components/dashboard-layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Clock,
  Copy,
  Cpu,
  HardDrive,
  MemoryStick,
  Monitor,
  Play,
  RefreshCw,
  Square,
  Terminal,
} from "lucide-react";
import Link from "next/link";
import { BACKEND_URL } from "@/app/constants";
import { fetchJWTToken } from "@/lib/utils";

interface BastionConfig {
  address: string;
  port: number;
  publicKey: string;
}

interface Resource {
  id: string;
  name: string;
  cpu: number;
  ram: number;
  storage: number;
  gpu?: {
    count: number;
    vram: number;
  };
  status: "pending" | "installing" | "ready" | "active" | "error";
  createdAt: string;
  updatedAt: string;
  bastionConfig?: BastionConfig;
  checkedSpecs?: {
    cpu?: string;
    ram?: string;
    disk?: string;
    os?: string;
    resourceId?: string;
    macAddress?: string;
    ipAddress?: string;
  };
}

// Reusable component for displaying resource specifications
const ResourceSpecs = ({ resource }: { resource: Resource }) => (
  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
    <div className="flex items-center gap-2">
      <Cpu className="h-4 w-4 text-muted-foreground" />
      <span className="text-sm">{resource.cpu} CPU cores</span>
    </div>
    <div className="flex items-center gap-2">
      <MemoryStick className="h-4 w-4 text-muted-foreground" />
      <span className="text-sm">{resource.ram} GB RAM</span>
    </div>
    <div className="flex items-center gap-2">
      <HardDrive className="h-4 w-4 text-muted-foreground" />
      <span className="text-sm">{resource.storage} GB storage</span>
    </div>
    {resource.gpu && (
      <div className="flex items-center gap-2">
        <Monitor className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm">
          {resource.gpu.count} GPU{resource.gpu.count !== 1 ? "s" : ""}
        </span>
      </div>
    )}
    <div className="flex items-center gap-2">
      <Terminal className="h-4 w-4 text-muted-foreground" />
      <span className="text-sm">IP : {resource.checkedSpecs?.ipAddress}</span>
    </div>

  </div>
);

export default function ResourceDetailPage() {
  const params = useParams();
//  const router = useRouter();
  const [resource, setResource] = useState<Resource | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [jwtToken, setJwtToken] = useState<string>("");

  const resourceId = params.id as string;

  // Memoize fetchResource to make it a stable dependency for useEffect
  const fetchResource = useCallback(async () => {

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${BACKEND_URL}/api/resources/${resourceId}`, {
        credentials: "include",
      });


      if (!response.ok) {
        throw new Error("Failed to fetch resource");
      }

      const data = await response.json();
        if (Array.isArray(data) && data.length > 0) {
          setResource(data[0]); // Take the first item from the array
        } else if (data && typeof data === 'object' && !Array.isArray(data)) {
          // If the backend occasionally sends a single object directly
          setResource(data);
        } else {
          setError("Invalid resource data format received from backend.");
          setResource(null);
        }


    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  }, [resourceId]); // fetchResource depends on resourceId

  // Initial fetch when component mounts or resourceId changes
  useEffect(() => {
    fetchResource();
  }, [fetchResource]);

  // Fetch JWT token on component mount
  useEffect(() => {
    const getToken = async () => {
      try {
        const token = await fetchJWTToken();
        setJwtToken(token);
      } catch (error) {
        console.error("Failed to fetch JWT token:", error);
      }
    };
    getToken();
  }, []); // fetchResource is now stable

  // Separate useEffect for polling based on resource status
  useEffect(() => {
    let pollInterval: NodeJS.Timeout;

    if (resource?.status === 'pending' || resource?.status === 'installing') {
      pollInterval = setInterval(() => {
        fetchResource();
      }, 21000); // Poll every 21 seconds
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    };
  }, [resource?.status, fetchResource]); // Add fetchResource to dependencies

  // Generate install command with resource ID and bastion config
  const getInstallCommand = (resourceId: string) => {
    const bastionConfig = resource?.bastionConfig;
    if (!bastionConfig) {
      return `curl -sSL https://raw.githubusercontent.com/kollectyve-labs/kumulus-tools/refs/heads/main/flare-provider-setup.sh | RESOURCE_ID=${resourceId} PROVIDER_TOKEN=${jwtToken} bash`;
    }

    return `curl -sSL https://raw.githubusercontent.com/kollectyve-labs/kumulus-tools/refs/heads/main/flare-provider-setup.sh | RESOURCE_ID=${resourceId} PROVIDER_TOKEN=${jwtToken} BASTION_ADDRESS="${bastionConfig.address}" BASTION_PORT=${bastionConfig.port} BASTION_PUBKEY="${bastionConfig.publicKey}" bash`;
  };

  // Generate truncated install command for display
  const getInstallCommandDisplay = () => {
      return `curl -sSL https://..../flare-provider-setup.sh | bash`;
  };

  const copyInstallCommand = () => {
    navigator.clipboard.writeText(getInstallCommand(resourceId));
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleStartProviding = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/resources/${resourceId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ status: "active" }),
      });

      if (!response.ok) {
        throw new Error("Failed to start providing");
      }

      fetchResource();
    } catch (error) {
      console.error("Error starting providing:", error);
    }
  };

  const handleStopProviding = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/resources/${resourceId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ status: "ready" }),
      });

      if (!response.ok) {
        throw new Error("Failed to stop providing");
      }

      fetchResource();
    } catch (error) {
      console.error("Error stopping providing:", error);
    }
  };

  const getStatusInfo = (status: Resource["status"]) => {
    switch (status) {
      case "pending":
        return {
          badge: (
            <Badge
              variant="secondary"
              className="bg-yellow-100 text-yellow-800"
            >
              <Clock className="h-3 w-3 mr-1" />Step 1: Pending Setup
            </Badge>
          ),
          step: 1,
          title: "Resource Registered",
          description:
            "Your resource has been registered. Now install the Kumulus agent.",
        };
      case "installing":
        return {
          badge: (
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />Step 2:
              Installing
            </Badge>
          ),
          step: 2,
          title: "Installing Agent",
          description: "The Kumulus agent is being installed and configured.",
        };
      case "ready":
        return {
          badge: (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />Step 3: Ready to Provide
            </Badge>
          ),
          step: 3,
          title: "Ready to Provide",
          description:
            "Your machine is configured and ready to start providing resources.",
        };
      case "active":
        return {
          badge: (
            <Badge variant="default" className="bg-green-600">
              <CheckCircle className="h-3 w-3 mr-1" />Active - Providing
            </Badge>
          ),
          step: 4,
          title: "Currently Providing",
          description:
            "Your machine is actively providing computing resources.",
        };
      case "error":
        return {
          badge: (
            <Badge variant="destructive">
              <AlertCircle className="h-3 w-3 mr-1" />Error
            </Badge>
          ),
          step: 0,
          title: "Setup Error",
          description:
            "There was an issue during setup. Please check the logs and try again.",
        };
      default:
        return {
          badge: <Badge variant="secondary">Unknown</Badge>,
          step: 0,
          title: "Unknown Status",
          description: "The resource status is unknown.",
        };
    }
  };

  // Consolidate loading and error states
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading resource...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !resource) {
    return (
      <DashboardLayout>
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading Resource
            </CardTitle>
            <CardDescription>{error || "Resource not found"}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button onClick={fetchResource} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Link href="/resources">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Resources
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </DashboardLayout>
    );
  }

  const statusInfo = getStatusInfo(resource.status);

  // Pending state: Show step-by-step installation like /resources/add
  const renderPendingView = () => {
    return (
      <div className="space-y-6">
        {/* Step Progress */}
        <Card>
          <CardHeader>
            <CardTitle>Setup Your Resource</CardTitle>
            <CardDescription>
              Follow these steps to get your resource online and start providing computing power
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Step 1: Registration (Completed) */}
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center">
                    <CheckCircle className="h-4 w-4" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-green-800">Step 1: Resource Registration</h3>
                  <p className="text-sm text-muted-foreground">Resource successfully registered with Kumulus</p>
                </div>
              </div>

              {/* Step 2: Install Agent (Current) */}
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center">
                    <Terminal className="h-4 w-4" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-blue-800">Step 2: Install Kumulus Agent</h3>
                  <div className="mt-3 space-y-4">
                    <p className="text-sm text-muted-foreground">
                      Run this command on your machine to install and configure the Kumulus agent:
                    </p>
                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
                      <div className="flex items-center justify-between">
                        <code className="flex-1 break-all">
                          {getInstallCommandDisplay()}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={copyInstallCommand}
                          className="text-green-400 hover:text-green-300 ml-2"
                        >
                          {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-800 mb-2">What this script does:</h4>
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• Installs Docker and required dependencies</li>
                        <li>• Downloads and configures the Kumulus agent</li>
                        <li>• Verifies your machine specifications</li>
                        <li>• Connects your resource to the Kumulus network</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Step 3: Start Providing (Disabled) */}
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-100 text-gray-400 rounded-full flex items-center justify-center">
                    <Play className="h-4 w-4" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-600">Step 3: Start Providing</h3>
                  <p className="text-sm text-muted-foreground">Complete the installation to enable providing</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Resource Specs */}
        <Card>
          <CardHeader>
            <CardTitle>Resource Specifications</CardTitle>
          </CardHeader>
          <CardContent>
            <ResourceSpecs resource={resource} />
          </CardContent>
        </Card>
      </div>
    );
  };

  // Installing state: Show progress
  const renderInstallingView = () => {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Installation in Progress</CardTitle>
          <CardDescription>The Kumulus agent is being installed on your machine</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
              <span className="text-sm">Installing components...</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full transition-all duration-500" style={{ width: '75%' }}></div>
            </div>
            <p className="text-xs text-muted-foreground">This may take a few minutes. Please wait...</p>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Ready state: Show resource info with Start Providing button
  const renderReadyView = () => {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Resource Ready
            </CardTitle>
            <CardDescription>
              Your machine is configured and ready to start providing computing resources
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <ResourceSpecs resource={resource} />
              <Button onClick={handleStartProviding} className="w-full" size="lg">
                <Play className="h-4 w-4 mr-2" />
                Start Providing
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Active state: Show stats with Stop Providing button
  const renderActiveView = () => {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Currently Providing
            </CardTitle>
            <CardDescription>
              Your machine is actively providing computing resources to the network
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Resource Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">24/7</div>
                  <div className="text-sm text-muted-foreground">Uptime</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">45%</div>
                  <div className="text-sm text-muted-foreground">CPU Usage</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">2.1 GB</div>
                  <div className="text-sm text-muted-foreground">RAM Used</div>
                </div>
              </div>

              {/* Resource Specs */}
              <div className="border-t pt-4">
                <h4 className="font-semibold mb-3">Resource Specifications</h4>
                <ResourceSpecs resource={resource} />
              </div>

              <Button onClick={handleStopProviding} variant="outline" className="w-full" size="lg">
                <Square className="h-4 w-4 mr-2" />
                Stop Providing
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Error state: Show error with retry option
  const renderErrorView = () => {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-800">
            <AlertCircle className="h-5 w-5" />
            Installation Error
          </CardTitle>
          <CardDescription>
            There was an issue during the installation process
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-red-700">
              The installation script encountered an error. Please try running the command again.
            </p>
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
              <div className="flex items-center justify-between">
                <code className="flex-1 break-all">
                  {getInstallCommandDisplay()}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyInstallCommand}
                  className="text-green-400 hover:text-green-300 ml-2"
                >
                  {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Default view: Fallback
  const renderDefaultView = () => {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Resource Details</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Loading resource information...</p>
        </CardContent>
      </Card>
    );
  };

  // Render different views based on resource status
  const renderResourceView = () => {
    switch (resource.status) {
      case 'pending':
        return renderPendingView();
      case 'installing':
        return renderInstallingView();
      case 'ready':
        return renderReadyView();
      case 'active':
        return renderActiveView();
      case 'error':
        return renderErrorView();
      default:
        return renderDefaultView();
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-4xl">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href="/resources">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Resources
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold tracking-tight">
              {resource.name}
            </h1>
            <p className="text-muted-foreground">
              Resource ID: {resource.id}
            </p>
          </div>
          <div>
            {statusInfo.badge}
          </div>
        </div>

        {/* Dynamic content based on status */}
        {renderResourceView()}
      </div>
    </DashboardLayout>
  );
}